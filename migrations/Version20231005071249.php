<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231005071249 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE import ADD additional_data JSON DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN import.additional_data IS \'(DC2Type:json_array)\'');
        $this->addSql('ALTER TABLE value ADD store_id VARCHAR(255) NOT NULL');
        $this->addSql('COMMENT ON COLUMN value.store_id IS \'(DC2Type:store_id)\'');
        $this->addSql('ALTER TABLE value ADD CONSTRAINT FK_1D775834B092A811 FOREIGN KEY (store_id) REFERENCES store (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_1D775834B092A811 ON value (store_id)');
        $this->addSql('CREATE UNIQUE INDEX group_value_idx ON value (group_of_values_id, value)');
        $this->addSql('CREATE INDEX valueIdx ON value (value)');
        $this->addSql('CREATE INDEX groupOfValuesIdx ON value (group_of_values_id)');
        $this->addSql('CREATE INDEX value_lower_idx ON "value" (lower(value));');
    }
}
