<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241217141859 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX IF NOT EXISTS memberAchievementProgressReadModelRequiredOnIdx ON member_achievement_progress_read_model (recreate_required_on)');
        $this->addSql('CREATE INDEX IF NOT EXISTS memberCampaignUsagesReadModelRequiredOnIdx ON member_campaign_usages_read_model (recreate_required_on)');
        $this->addSql('CREATE INDEX IF NOT EXISTS walletReadModelRequiredOnIdx ON wallet_read_model (recreate_required_on)');
    }
}
