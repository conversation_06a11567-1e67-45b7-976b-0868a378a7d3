<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221125134921 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Improving performance with indices';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX email_lower_idx ON "user" (lower(email))');
        $this->addSql('CREATE INDEX doc_number_lower_idx ON transaction (lower(document_number))');
        $this->addSql('CREATE INDEX loyalty_card_number_lower_idx ON "user" (lower(loyalty_card_number))');
        $this->addSql('CREATE INDEX phone_lower_idx ON "user" (lower(phone))');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX email_lower_idx');
        $this->addSql('DROP INDEX doc_number_lower_idx');
        $this->addSql('DROP INDEX loyalty_card_number_lower_idx');
        $this->addSql('DROP INDEX phone_lower_idx');
    }
}
