<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221114130305 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE achievement_member_progress ADD unique_referees JSON DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN achievement_member_progress.unique_referees IS \'(DC2Type:json_array)\'');
        $this->addSql('ALTER TABLE achievement_rule ADD unique_referee BOOLEAN DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE achievement_member_referees DROP unique_referees');
        $this->addSql('ALTER TABLE achievement_rule DROP unique_referee');
    }
}
