<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Translation\Infrastructure\Form\Type;

use OpenLoyalty\Translation\Domain\DTO\TranslationEntryData;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Locale;
use Symfony\Component\Validator\Constraints\NotBlank;

class TranslationEntryDataFormType extends AbstractType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('languageCode', TextType::class, [
            'constraints' => [new Locale()],
            'required' => false,
        ]);
        $builder->add('key', TextType::class, [
            'required' => false,
        ]);
        $builder->add('value', TextType::class, [
            'constraints' => [new NotBlank()],
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => TranslationEntryData::class,
        ]);
    }
}
