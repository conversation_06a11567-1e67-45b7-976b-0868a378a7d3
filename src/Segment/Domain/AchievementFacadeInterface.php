<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Domain;

use Generator;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\AchievementRuleId;
use OpenLoyalty\Core\Domain\Id\SegmentId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\ValueObject\Achievement\RuleDetails;
use OpenLoyalty\Segment\Domain\Params\AchievementRuleProgressParams;
use OpenLoyalty\Segment\Domain\Params\CountParams;
use OpenLoyalty\Segment\Domain\ValueObject\ResourceAssociatedToSegment;

interface AchievementFacadeInterface
{
    public function isAchievementExistsInStore(AchievementId $achievementId, StoreId $storeId): bool;

    public function isAchievementRuleExistsInStore(AchievementRuleId $achievementRuleId, StoreId $storeId): bool;

    public function isRuleExistsInAchievement(AchievementRuleId $achievementRuleId, AchievementId $achievementId): bool;

    public function getAchievementRuleDetails(AchievementRuleId $achievementRuleId): ?RuleDetails;

    public function getAllMemberIdsWithAchievementCompletionCount(
        StoreId $storeId,
        AchievementId $achievementId,
        CountParams $params
    ): Generator;

    public function getAllMembersIdsWithAchievementProgress(
        StoreId $storeId,
        AchievementRuleId $achievementRuleId,
        AchievementId $achievementId,
        AchievementRuleProgressParams $params
    ): Generator;

    public function getAllMembersIdsWithAchievementConsecutiveCompletedPeriodsProgress(
        StoreId $storeId,
        AchievementRuleId $achievementRuleId,
        AchievementId $achievementId,
        AchievementRuleProgressParams $params
    ): Generator;

    /**
     * @return array<ResourceAssociatedToSegment>
     */
    public function findAchievementsAssociatedToSegment(SegmentId $segmentId, StoreId $storeId): array;
}
