<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Segment\Infrastructure\Validator\Constraints;

use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Segment\Domain\Exception\CustomerNotFoundException;
use OpenLoyalty\Segment\Domain\Exception\TooManyCustomersFoundException;
use OpenLoyalty\Segment\Infrastructure\Provider\CustomerIdProvider;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;

class CustomerValidator extends ConstraintValidator
{
    public function __construct(
        private readonly CustomerIdProvider $customerIdProvider,
        private readonly TranslatorInterface $translator,
        private readonly StoreContextProviderInterface $storeContextProvider
    ) {
    }

    /**
     * {@inheritdoc}
     */
    public function validate(mixed $value, Constraint $constraint): void
    {
        try {
            $storeId = $this->storeContextProvider->getStore()->getStoreId();
            $this->customerIdProvider->getCustomerId($value, $storeId);
        } catch (TooManyCustomersFoundException|CustomerNotFoundException $e) {
            $this->context->buildViolation($this->translator->trans($e->getMessage(), ['%data%' => $value]))
                ->addViolation();
        }
    }
}
