<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Segment\Infrastructure\Message\Lock;

use OpenLoyalty\Core\Domain\Message\ThreadSafeTransportableInterface;
use OpenLoyalty\Core\Infrastructure\Message\Lock\LockItemPropertiesProviderInterface;
use OpenLoyalty\Core\Infrastructure\Message\Lock\LockProperties;
use OpenLoyalty\Segment\Application\Job\RecreateSegmentJob;

final readonly class RecreateSegmentLockItemPropertiesProvider implements LockItemPropertiesProviderInterface
{
    public function __construct(
        private int $lockTtl
    ) {
    }

    public function supports(ThreadSafeTransportableInterface $message): bool
    {
        return $message instanceof RecreateSegmentJob;
    }

    public function getProperties(ThreadSafeTransportableInterface $message): ?LockProperties
    {
        if (!$message instanceof RecreateSegmentJob) {
            throw new \InvalidArgumentException();
        }

        return new LockProperties(
            'recreate_segment_'.$message->getSegmentId(),
            true,
            $this->lockTtl
        );
    }
}
