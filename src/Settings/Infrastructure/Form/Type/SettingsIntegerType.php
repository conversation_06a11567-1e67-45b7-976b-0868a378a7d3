<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Settings\Infrastructure\Form\Type;

use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Settings\Infrastructure\Form\DataTransformer\IntegerSettingDataTransformer;
use OpenLoyalty\Settings\Infrastructure\Service\SettingsManager;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\FormBuilderInterface;

/**
 * Class SettingsIntegerType.
 */
class SettingsIntegerType extends AbstractType
{
    /**
     * @var SettingsManager
     */
    private $settingsManager;

    /**
     * @var StoreContextProviderInterface
     */
    protected $storeContextProvider;

    public function __construct(SettingsManager $settingsManager, StoreContextProviderInterface $storeContextProvider)
    {
        $this->settingsManager = $settingsManager;
        $this->storeContextProvider = $storeContextProvider;
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->addModelTransformer(
            new IntegerSettingDataTransformer(
                $builder->getName(),
                $this->settingsManager,
                $this->storeContextProvider
            )
        );
    }

    /**
     * {@inheritdoc}
     */
    public function getParent()
    {
        return IntegerType::class;
    }
}
