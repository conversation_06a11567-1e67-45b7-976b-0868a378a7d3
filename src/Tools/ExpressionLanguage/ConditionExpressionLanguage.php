<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Tools\ExpressionLanguage;

use Exception;
use OpenLoyalty\Core\Domain\Condition\Schema\ConditionExpressionBuilder;
use OpenLoyalty\Core\Domain\ValueFacadeInterface;
use OpenLoyalty\Tools\ExpressionLanguage\Processor\ProcessorInterface;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Convert\ToDateProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Custom\AggregateProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Custom\FilterProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Custom\GroupOfValuesProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Custom\PercentValueDistributionProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Number\RoundDownExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Number\RoundUpExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\String\EndsWithExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\String\LowerExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\String\StartsWithExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Time\GetDateInFutureByDaysExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Time\GetDateInFutureByMonthsExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Time\GetDateInFutureByYearsExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Time\GetDayNameOfWeekExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Time\GetDayOfMonthExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Time\GetMonthNameExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Time\IsAfterExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Time\IsBeforeExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Time\IsBetweenExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Time\IsNotBetweenExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Time\IsTimeBetweenExpressionLanguageProvider;
use OpenLoyalty\Tools\ExpressionLanguage\Provider\Time\TimestampExpressionLanguageProvider;
use Psr\Cache\CacheItemPoolInterface;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage as BaseExpressionLanguage;

class ConditionExpressionLanguage extends BaseExpressionLanguage implements ConditionExpressionLanguageInterface
{
    public function __construct(
        private readonly ProcessorInterface $processor,
        ValueFacadeInterface $valueFacade,
        ConditionExpressionBuilder $conditionExpressionBuilder,
        int $limitForDaysInFuture,
        int $limitForMonthsInFuture,
        int $limitForYearsInFuture,
        CacheItemPoolInterface $cache = null,
        array $providers = [],
    ) {
        $providers = [
            new RoundDownExpressionLanguageProvider(),
            new RoundUpExpressionLanguageProvider(),
            new IsAfterExpressionLanguageProvider(),
            new IsBeforeExpressionLanguageProvider(),
            new GetDayNameOfWeekExpressionLanguageProvider(),
            new GetMonthNameExpressionLanguageProvider(),
            new IsBetweenExpressionLanguageProvider(),
            new IsNotBetweenExpressionLanguageProvider(),
            new StartsWithExpressionLanguageProvider(),
            new EndsWithExpressionLanguageProvider(),
            new LowerExpressionLanguageProvider(),
            new ToDateProvider(),
            new AggregateProvider(),
            new GroupOfValuesProvider($valueFacade),
            new IsTimeBetweenExpressionLanguageProvider(),
            new GetDayOfMonthExpressionLanguageProvider(),
            new TimestampExpressionLanguageProvider(),
            new PercentValueDistributionProvider(),
            new GetDateInFutureByDaysExpressionLanguageProvider($limitForDaysInFuture),
            new GetDateInFutureByMonthsExpressionLanguageProvider($limitForMonthsInFuture),
            new GetDateInFutureByYearsExpressionLanguageProvider($limitForYearsInFuture),
            new FilterProvider($conditionExpressionBuilder, $this),
            ...$providers,
        ];

        parent::__construct($cache, $providers);
    }

    /**
     * @param Expression|string $expression
     * @param array<mixed>      $context
     *
     * @return mixed
     */
    public function evaluate(Expression|string $expression, array $context = []): mixed
    {
        try {
            return parent::evaluate((string) $this->processor->process((string) $expression), $context);
        } catch (Exception $ex) {
            throw new InvalidExpressionException(sprintf('Invalid expression: %s. Caused by: %s', $expression, $ex->getMessage()));
        }
    }
}
