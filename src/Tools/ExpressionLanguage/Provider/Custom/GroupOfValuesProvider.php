<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Tools\ExpressionLanguage\Provider\Custom;

use OpenLoyalty\Core\Domain\ValueFacadeInterface;
use OpenLoyalty\Tools\ExpressionLanguage\CompileNotSupportedException;
use OpenLoyalty\Tools\ExpressionLanguage\ContextItem\GroupValues;
use Symfony\Component\ExpressionLanguage\ExpressionFunction;
use Symfony\Component\ExpressionLanguage\ExpressionFunctionProviderInterface;

class GroupOfValuesProvider implements ExpressionFunctionProviderInterface
{
    public function __construct(
        private readonly ValueFacadeInterface $valueFacade
    ) {
    }

    public function getFunctions(): array
    {
        return [
            new ExpressionFunction(
                'groupValues',
                function ($collection): void {
                    throw new CompileNotSupportedException();
                },
                function (array $arguments, $argument) {
                    return new GroupValues($argument, $this->valueFacade);
                }
            ),
        ];
    }
}
