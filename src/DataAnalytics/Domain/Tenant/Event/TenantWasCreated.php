<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Tenant\Event;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AbstractAnalyticsEvent;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\TenantCode;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version;

final class TenantWasCreated extends AbstractAnalyticsEvent
{
    public function __construct(
        AnalyticsEventId $analyticsEventId,
        Version $eventVersion,
        StoreId $tenantId,
        private readonly TenantCode $tenantCode,
        private readonly DateTimeImmutable $tenantCreationDate
    ) {
        parent::__construct($analyticsEventId, $eventVersion, $tenantId);
    }

    public function getTenantCode(): TenantCode
    {
        return $this->tenantCode;
    }

    public function getTenantCreationDate(): DateTimeImmutable
    {
        return $this->tenantCreationDate;
    }
}
