<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Unit\Event;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AbstractAnalyticsEvent;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version;

final class UnitsWasUnlocked extends AbstractAnalyticsEvent
{
    public function __construct(
        AnalyticsEventId $analyticsEventId,
        Version $eventVersion,
        StoreId $tenantId,
        private readonly TransferId $transferId,
        private readonly CustomerId $memberId,
        private readonly string $walletTypeCode,
        private readonly float $value,
        private readonly DateTimeImmutable $unlockedDate,
        private readonly ?DateTimeImmutable $cancellationDate = null
    ) {
        parent::__construct($analyticsEventId, $eventVersion, $tenantId);
    }

    public function getTransferId(): TransferId
    {
        return $this->transferId;
    }

    public function getMemberId(): CustomerId
    {
        return $this->memberId;
    }

    public function getWalletTypeCode(): string
    {
        return $this->walletTypeCode;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function getUnlockedDate(): DateTimeImmutable
    {
        return $this->unlockedDate;
    }

    public function getCancellationDate(): ?DateTimeImmutable
    {
        return $this->cancellationDate ?? null;
    }
}
