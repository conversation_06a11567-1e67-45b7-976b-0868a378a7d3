<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Member\Event;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\DataAnalytics\Domain\Member\ValueObject\ActiveMemberSettings;
use OpenLoyalty\DataAnalytics\Domain\Member\ValueObject\ActivityType;
use OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AbstractAnalyticsEvent;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version;

final class MemberHasBecomeInactive extends AbstractAnalyticsEvent
{
    public function __construct(
        AnalyticsEventId $analyticsEventId,
        Version $eventVersion,
        private readonly CustomerId $memberId,
        private readonly DateTimeImmutable $memberDeactivationDate,
        private readonly ActiveMemberSettings $activeMemberSettings,
        StoreId $tenantId
    ) {
        parent::__construct($analyticsEventId, $eventVersion, $tenantId);
    }

    public function getMemberId(): CustomerId
    {
        return $this->memberId;
    }

    public function getMemberDeactivationDate(): DateTimeImmutable
    {
        return $this->memberDeactivationDate;
    }

    public function getActiveMemberSettings(): ActiveMemberSettings
    {
        return $this->activeMemberSettings;
    }

    public function getActivityType(): ActivityType
    {
        return new ActivityType(ActivityType::INACTIVE);
    }
}
