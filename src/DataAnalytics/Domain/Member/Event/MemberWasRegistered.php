<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\DataAnalytics\Domain\Member\Event;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Message\ThreadSafeTransportableInterface;
use OpenLoyalty\DataAnalytics\Domain\Member\ValueObject\Gender;
use OpenLoyalty\DataAnalytics\Domain\Shared\Identifier\AnalyticsEventId;
use OpenLoyalty\DataAnalytics\Domain\Shared\Message\AbstractAnalyticsEvent;
use OpenLoyalty\DataAnalytics\Domain\Shared\ValueObject\Version;

final class MemberWasRegistered extends AbstractAnalyticsEvent implements ThreadSafeTransportableInterface
{
    public function __construct(
        AnalyticsEventId $analyticsEventId,
        Version $eventVersion,
        private readonly CustomerId $memberId,
        private readonly DateTimeImmutable $memberRegistrationDate,
        private readonly Gender $gender,
        StoreId $tenantId
    ) {
        parent::__construct($analyticsEventId, $eventVersion, $tenantId);
    }

    public function getMemberId(): CustomerId
    {
        return $this->memberId;
    }

    public function getMemberRegistrationDate(): DateTimeImmutable
    {
        return $this->memberRegistrationDate;
    }

    public function getGender(): Gender
    {
        return $this->gender;
    }
}
