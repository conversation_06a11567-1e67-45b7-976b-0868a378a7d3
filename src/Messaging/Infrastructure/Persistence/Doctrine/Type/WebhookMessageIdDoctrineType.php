<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Infrastructure\Persistence\Doctrine\Type;

use Assert\AssertionFailedException;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use OpenLoyalty\Core\Domain\Id\WebhookMessageId;
use Ramsey\Uuid\Doctrine\UuidType;

final class WebhookMessageIdDoctrineType extends UuidType
{
    public const NAME = 'webhook_message_id';

    /**
     * @param                           $value string|WebhookMessageId|null
     * @throws AssertionFailedException
     */
    public function convertToPHPValue($value, AbstractPlatform $platform): ?WebhookMessageId
    {
        if (empty($value)) {
            return null;
        }

        if ($value instanceof WebhookMessageId) {
            return $value;
        }

        return new WebhookMessageId($value); // @phpstan-ignore argument.type
    }

    /**
     * @param string|WebhookMessageId|null $value
     */
    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ($value instanceof WebhookMessageId) {
            return (string) $value;
        }

        if (!empty($value)) {
            return $value;
        }

        return null;
    }

    public function getName(): string
    {
        return self::NAME;
    }
}
