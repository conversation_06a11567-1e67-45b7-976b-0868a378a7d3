<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Messaging\Domain\Notification;

use OpenLoyalty\Messaging\Domain\DTO\Recipient;
use OpenLoyalty\Messaging\Domain\Notification\Transport\EmailNotificationTransportInterface;
use OpenLoyalty\User\Domain\Service\CodeFactoryInterface;
use OpenLoyalty\User\Domain\Service\CodeManagerInterface;
use OpenLoyalty\User\Domain\UserInterface;

class ConfirmNotificationSender implements ConfirmNotificationSenderInterface
{
    public function __construct(
        private CodeManagerInterface $codeManager,
        private EmailNotificationTransportInterface $emailNotificationTransport,
        private array $parameters,
        private CodeFactoryInterface $codeFactory
    ) {
    }

    public function sendPasswordResetForAdmin(UserInterface $user): void
    {
        $code = $this->codeFactory->createCode(get_class($user), $user->getId(), CodeManagerInterface::USER_PASSWORD_RESET);

        $recipient = new Recipient(
            $user->getEmail(),
            $user->getPhone()
        );

        $codeNo = $this->codeManager->getCodeSequenceNumber($code);

        if (null === $codeNo) {
            return;
        }

        $params = [
            'link' => $this->parameters['admin_url'].$this->parameters['reset_password_url'].'/'.$code->getCode(),
            'code' => $code->getCode(),
            'code_number' => $codeNo,
        ];

        $this->emailNotificationTransport->sendPasswordReset($recipient, $params);
    }
}
