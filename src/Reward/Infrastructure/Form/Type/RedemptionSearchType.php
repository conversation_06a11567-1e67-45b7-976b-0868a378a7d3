<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Infrastructure\Form\Type;

use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Infrastructure\Search\Doctrine\CriteriaBuilder\BaseCriteriaBuilder;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\DateTimeCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\KeyValueListCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\NumericCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\TextCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\TextListCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\UuidCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\Criteria\UuidListCriteriaType;
use OpenLoyalty\Core\Infrastructure\Search\Form\Symfony\SearchType;
use OpenLoyalty\Core\Infrastructure\Validator\Constraints\DateTimeCriteriaRangeValid;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class RedemptionSearchType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('rewardId', UuidCriteriaType::class, [
            'field_name' => 'reward',
        ]);
        $builder->add('name', TextCriteriaType::class);
        $builder->add('rewardType', TextCriteriaType::class, [
            'default_operator' => CriteriaInterface::EQUAL,
            'allowed_operators' => [CriteriaInterface::EQUAL],
        ]);
        $builder->add('token', TextCriteriaType::class);
        $builder->add('status', TextListCriteriaType::class, [
            'default_operator' => CriteriaInterface::EQUAL,
            'allowed_operators' => [CriteriaInterface::IN, CriteriaInterface::EQUAL],
        ]);
        $builder->add('costInPoints', NumericCriteriaType::class);
        $builder->add('redemptionDate', DateTimeCriteriaType::class,
            ['constraints' => [new DateTimeCriteriaRangeValid()]]
        );
        $builder->add('customerId', UuidCriteriaType::class);
        $builder->add('customerData:email', TextCriteriaType::class);
        $builder->add('customerData:firstName', TextCriteriaType::class);
        $builder->add('customerData:lastName', TextCriteriaType::class);
        $builder->add('customerData:phone', TextCriteriaType::class);
        $builder->add('customerData:loyaltyCardNumber', TextCriteriaType::class);
        $builder->add('actionCause:transactionId', UuidCriteriaType::class);
        $builder->add('actionCause:customerId', UuidCriteriaType::class);
        $builder->add('actionCause:campaignId', UuidCriteriaType::class);
        $builder->add('actionCause:customEventId', UuidCriteriaType::class);
        $builder->add('actionCause:internalEventName', TextCriteriaType::class);
        $builder->add('reward:categories', UuidListCriteriaType::class, [
            'field_name' => BaseCriteriaBuilder::ABSOLUTE_PATH_SIGN.'r.categories',
            'default_operator' => CriteriaInterface::IN,
        ]);
        $builder->add('reward:labels', KeyValueListCriteriaType::class, [
            'field_name' => BaseCriteriaBuilder::ABSOLUTE_PATH_SIGN.'r.labels',
            'default_operator' => CriteriaInterface::EQUAL,
        ]);
    }

    public function getParent(): string
    {
        return SearchType::class;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'store_field' => 'storeId',
            'field_mappings' => [
                'rewardId' => 'reward',
                'reward:categories' => BaseCriteriaBuilder::ABSOLUTE_PATH_SIGN.'r.categories',
                'reward:labels' => BaseCriteriaBuilder::ABSOLUTE_PATH_SIGN.'r.labels',
            ],
            'default_order_by' => 'createdAt',
        ]);
    }
}
