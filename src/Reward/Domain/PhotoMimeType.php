<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Reward\Domain;

use OpenLoyalty\Reward\Domain\Exception\InvalidPhotoMimeTypeException;

class PhotoMimeType
{
    private const SUPPORTED_MIME_TYPES = ['image/gif', 'image/jpg', 'image/png', 'image/jpeg'];

    /**
     * @var string
     */
    private $value;

    public function __construct(string $type)
    {
        if (empty($type) || !$this->expectedMimeType($type)) {
            throw InvalidPhotoMimeTypeException::create(implode(', ', PhotoMimeType::SUPPORTED_MIME_TYPES));
        }

        $this->value = $type;
    }

    public function __toString(): string
    {
        return $this->value;
    }

    private function expectedMimeType(string $type): bool
    {
        return in_array($type, self::SUPPORTED_MIME_TYPES);
    }
}
