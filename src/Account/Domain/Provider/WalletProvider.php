<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Account\Domain\Provider;

use OpenLoyalty\Account\Domain\Exception\DefaultAccountNotFoundException;
use OpenLoyalty\Account\Domain\Exception\WalletNotFoundException;
use OpenLoyalty\Account\Domain\Wallet;
use OpenLoyalty\Account\Domain\WalletRepositoryInterface;
use OpenLoyalty\Account\Domain\WalletRepositoryReadContextInterface;
use OpenLoyalty\Core\Domain\Id\AccountId;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Model\Wallet as WalletVO;

final readonly class WalletProvider implements WalletProviderInterface
{
    public function __construct(
        private AccountProviderInterface $accountProvider,
        private WalletRepositoryInterface $walletRepository,
        private WalletRepositoryReadContextInterface $walletReadRepository
    ) {
    }

    public function provideForCustomer(
        StoreId $storeId,
        CustomerId $customerId,
        ?string $walletCode = null,
        bool $withAccount = false,
        bool $readConnection = false
    ): WalletVO {
        $wallet = $this->getWallet($storeId, $customerId, $walletCode, $readConnection);

        if (null === $wallet) {
            throw new WalletNotFoundException(sprintf('Wallet code=%s for customerId=%s not found', $walletCode, $customerId));
        }

        return $this->buildWallet($wallet, $withAccount, $readConnection);
    }

    /**
     * @return array<WalletVO>
     */
    public function provideAllForCustomer(
        StoreId $storeId,
        CustomerId $customerId,
        bool $withAccount = false,
        bool $readConnection = false
    ): array {
        $walletRepository = $readConnection ? $this->walletReadRepository : $this->walletRepository;
        $wallets = $walletRepository->byCustomer($storeId, $customerId);

        $result = [];
        foreach ($wallets as $wallet) {
            $result[$wallet->getType()->getCode()] = $this->buildWallet($wallet, $withAccount, $readConnection);
        }

        return $result;
    }

    public function provideById(
        AccountId $walletId,
        bool $withAccount = false,
        bool $readConnection = false
    ): WalletVO {
        $walletRepository = $readConnection ? $this->walletReadRepository : $this->walletRepository;
        $wallet = $walletRepository->byId($walletId);

        if (null === $wallet) {
            throw new WalletNotFoundException(sprintf('WalletId = %s', $walletId));
        }

        return $this->buildWallet($wallet, $withAccount, $readConnection);
    }

    private function buildWallet(
        Wallet $wallet,
        bool $withAccount = false,
        bool $readConnection = false
    ): WalletVO {
        $accountVO = null;
        if (true === $withAccount) {
            $accountVO = $this->accountProvider->provide($wallet, $readConnection);
        }

        return new WalletVO($wallet->getStoreId(), $wallet->getWalletId(), $wallet->getOwner(), $wallet->getType(), $accountVO);
    }

    private function getWallet(
        StoreId $storeId,
        CustomerId $customerId,
        ?string $walletCode = null,
        bool $readConnection = false
    ): ?Wallet {
        if (null !== $walletCode) {
            return $this->walletRepository->byType($storeId, $customerId, $walletCode);
        }

        $walletRepository = $readConnection ? $this->walletReadRepository : $this->walletRepository;
        $wallet = $walletRepository->getDefault($storeId, $customerId);

        if (null === $wallet) {
            throw new DefaultAccountNotFoundException(sprintf('CustomerId = %s', (string) $customerId));
        }

        return $wallet;
    }
}
