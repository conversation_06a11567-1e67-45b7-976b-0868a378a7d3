<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

namespace OpenLoyalty\Transaction\Domain\Event\Listener;

use OpenLoyalty\Core\Domain\Message\EventHandlerInterface;
use OpenLoyalty\Transaction\Domain\TransactionRepositoryInterface;
use OpenLoyalty\User\Domain\SystemEvent\TransactionWillBeMatchedSystemEvent;

class TransactionWillBeMatchedListener implements EventHandlerInterface
{
    public function __construct(private TransactionRepositoryInterface $transactionRepository)
    {
    }

    public function __invoke(TransactionWillBeMatchedSystemEvent $event): void
    {
        $transactionEntity = $this->transactionRepository->byId($event->getTransactionId());

        if ($transactionEntity) {
            $transactionEntity->increaseMatchAttempt();
            $this->transactionRepository->save($transactionEntity);
        }
    }
}
