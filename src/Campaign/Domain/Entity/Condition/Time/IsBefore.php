<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain\Entity\Condition\Time;

use DateTime;
use DateTimeInterface;
use OpenLoyalty\Campaign\Domain\Condition\InvalidConditionException;
use OpenLoyalty\Campaign\Domain\Entity\Condition\AbstractCondition;
use OpenLoyalty\Campaign\Domain\Entity\Condition\ConditionInterface;

class IsBefore extends AbstractCondition
{
    public const OPERATOR = 'is_before';

    /**
     * @var DateTimeInterface
     */
    private $value;

    public function __construct(string $attributeName, DateTimeInterface $before)
    {
        parent::__construct($attributeName);
        $this->value = $before;
    }

    public function getExpression(): string
    {
        return sprintf(
            "is_before(%s, to_date('%s'))",
            $this->getAttributeName(),
            $this->value->format(DateTime::ISO8601)
        );
    }

    public function getOperator(): string
    {
        return self::OPERATOR;
    }

    public static function fromArray(?string $attribute, $data): ConditionInterface
    {
        if (!$data instanceof DateTimeInterface) {
            throw new InvalidConditionException('Value is not a datetime');
        }

        return new self($attribute, $data);
    }

    public function getData()
    {
        return $this->value;
    }
}
