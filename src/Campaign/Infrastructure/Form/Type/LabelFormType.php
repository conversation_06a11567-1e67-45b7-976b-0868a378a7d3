<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class LabelFormType extends AbstractType
{
    public function __construct(
        private readonly int $customAttributeKeyMaxLength,
        private readonly int $customAttributeValueMaxLength
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('key', TextType::class, [
            'required' => true,
            'constraints' => [
                new NotBlank(),
                new Length(['max' => $this->customAttributeKeyMaxLength]),
            ],
        ]);
        $builder->add('value', TextType::class, [
            'required' => true,
            'constraints' => [
                new NotBlank(),
                new Length(['max' => $this->customAttributeValueMaxLength]),
            ],
        ]);
    }
}
