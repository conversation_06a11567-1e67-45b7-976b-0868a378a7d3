<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Infrastructure\Form\RewardCoupon;

use OpenLoyalty\Import\Application\DTO\ImportData;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\DataTransformerInterface;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\NotBlank;

/**
 * @implements DataTransformerInterface<mixed, mixed>
 */
final class RewardCouponType extends AbstractType implements DataTransformerInterface
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('additionalData', RewardAdditionalDataType::class, [
            'required' => true,
            'constraints' => [
                new NotBlank(),
            ],
        ]);

        $builder->add('file', FileType::class, [
            'required' => true,
            'constraints' => [
                new NotBlank(),
                new File([
                    'mimeTypes' => ['text/csv', 'text/plain'],
                ]),
            ],
        ]);

        $builder->addModelTransformer($this);
    }

    /**
     * @param  ?array<mixed> $value
     * @return ?array<mixed>
     */
    public function transform(mixed $value): ?array
    {
        return $value;
    }

    /**
     * @param array{file: UploadedFile, additionalData: ?array{rewardId: string}} $value
     */
    public function reverseTransform(mixed $value): ?ImportData
    {
        if (null === $value['file']) {
            return null;
        }

        return new ImportData(
            $value['file'],
            $value['additionalData'] ?? null
        );
    }
}
