<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Import\Domain;

use OpenLoyalty\Import\Domain\EntityConverter\EntityConverterInterface;
use OpenLoyalty\Import\Domain\ValueObject\Entity;
use OpenLoyalty\Import\Domain\ValueObject\ImportType;
use OpenLoyalty\Import\Domain\ValueObject\ItemData;

final readonly class ItemDataEntityConverter
{
    /**
     * @param iterable<EntityConverterInterface> $entityConverters
     */
    public function __construct(
        private iterable $entityConverters
    ) {
    }

    public function convertToEntity(
        ItemData $itemData,
        ImportType $importType
    ): ?Entity {
        foreach ($this->entityConverters as $converter) {
            if ($converter->supports($importType)) {
                return $converter->convertToEntity($itemData);
            }
        }

        return null;
    }
}
