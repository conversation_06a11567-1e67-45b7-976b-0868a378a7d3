<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Core\Domain;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\BillableReportItemId;
use OpenLoyalty\Core\Domain\Id\BillableTransactionsPerTenantId;
use OpenLoyalty\Core\Domain\Model\BlameableInterface;
use OpenLoyalty\Core\Domain\Model\BlameableTrait;
use OpenLoyalty\Core\Domain\Model\TimestampableInterface;
use OpenLoyalty\Core\Domain\Model\TimestampableTrait;

class BillableTransactionsPerTenant implements TimestampableInterface, BlameableInterface
{
    use TimestampableTrait;
    use BlameableTrait;

    public function __construct(
       private BillableTransactionsPerTenantId $billableTransactionsPerTenantId,
       private BillableReportItemId $billableReportItemId,
       private string $tenantId,
       private DateTimeImmutable $period,
       private int $billableTransactionsCount,
    ) {
    }

    public function getBillableTransactionsPerTenantId(): BillableTransactionsPerTenantId
    {
        return $this->billableTransactionsPerTenantId;
    }

    public function getTenantId(): string
    {
        return $this->tenantId;
    }

    public function getBillableTransactionsCount(): int
    {
        return $this->billableTransactionsCount;
    }

    public function getPeriod(): DateTimeImmutable
    {
        return $this->period;
    }

    public function getBillableReportItemId(): BillableReportItemId
    {
        return $this->billableReportItemId;
    }
}
