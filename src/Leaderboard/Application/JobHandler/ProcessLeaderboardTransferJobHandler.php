<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Application\JobHandler;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\Core\Domain\Message\JobHandlerInterface;
use OpenLoyalty\Leaderboard\Application\Job\ProcessLeaderboardTransferJob;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\BatchHandlerInterface;
use Symfony\Component\Messenger\Handler\BatchHandlerTrait;

final readonly class ProcessLeaderboardTransferJobHandler implements JobHandlerInterface, BatchHandlerInterface
{
    use BatchHandlerTrait;

    /** @var array<int, array{storeId: StoreId, transferId: TransferId}> */
    private array $jobs = [];
    
    /** @var array<int, callable> */
    private array $acks = [];

    /**
     * @param LoggerInterface $logger
     * @param int $batchSize
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly int $batchSize = 10
    ) {
    }

    /**
     * @param ProcessLeaderboardTransferJob $job
     * @param callable|null $ack
     * @return int|null
     */
    public function __invoke(ProcessLeaderboardTransferJob $job, callable $ack = null): ?int
    {
        if (null === $ack) {
            // If $ack is null, handle the message synchronously
            $this->processJob($job);
            return null;
        }

        // Add job to buffer
        $this->jobs[] = [
            'storeId' => $job->getStoreId(),
            'transferId' => $job->getTransferId(),
        ];

        // Add acknowledgment function to buffer
        $this->acks[] = $ack;

        // Check if we should process the buffer
        if ($this->shouldFlush($this->batchSize)) {
            $this->flush();
        }

        // Return the number of messages in the buffer
        return count($this->jobs);
    }

    /**
     * @param ProcessLeaderboardTransferJob $job
     * @return void
     */
    private function processJob(ProcessLeaderboardTransferJob $job): void
    {
        $this->logger->info(
            'Processing leaderboard transfer job',
            [
                'storeId' => $job->getStoreId()->__toString(),
                'transferId' => $job->getTransferId()->__toString(),
            ]
        );

        // Add logic for processing the transfer for the leaderboard
        // For example, update user ranking, add points to the leaderboard, etc.
    }

    /**
     * @return void
     */
    private function flush(): void
    {
        if (empty($this->jobs)) {
            return;
        }

        $this->logger->info(
            sprintf('Processing batch of %d leaderboard transfer jobs', count($this->jobs)),
            [
                'jobsCount' => count($this->jobs),
            ]
        );

        // Add logic for batch processing of transfers for the leaderboard
        // For example, update user rankings, add points to the leaderboard, etc.
        foreach ($this->jobs as $jobData) {
            // Process a single job within the batch
            $this->logger->info(
                'Processing job from batch',
                [
                    'storeId' => $jobData['storeId']->__toString(),
                    'transferId' => $jobData['transferId']->__toString(),
                ]
            );
        }

        // Acknowledge processing of all messages
        foreach ($this->acks as $ack) {
            $ack();
        }

        // Clear buffers
        $this->jobs = [];
        $this->acks = [];
    }
}