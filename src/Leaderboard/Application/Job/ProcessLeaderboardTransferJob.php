<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Application\Job;

use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Core\Domain\Id\TransferId;
use OpenLoyalty\Core\Domain\Message\JobInterface;

final readonly class ProcessLeaderboardTransferJob implements JobInterface
{
    public function __construct(
        private StoreId $storeId,
        private TransferId $transferId,
        private \DateTimeImmutable $createdAt
    ) {
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getTransferId(): TransferId
    {
        return $this->transferId;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }
}