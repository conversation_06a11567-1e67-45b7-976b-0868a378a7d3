<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Infrastructure\SystemEvent\Listener;

use OpenLoyalty\Core\Application\PublicEvent\Shared\TransferWasAdded;
use OpenLoyalty\Core\Domain\Message\EventHandlerInterface;
use OpenLoyalty\Core\Domain\Message\JobBusInterface;
use OpenLoyalty\Leaderboard\Application\Job\ProcessLeaderboardTransferJob;
use Symfony\Component\Messenger\Handler\MessageSubscriberInterface;

final readonly class TransferWasAddedListener implements EventHandlerInterface, MessageSubscriberInterface
{
    public function __construct(
        private JobBusInterface $jobBus
    ) {
    }

    public static function getHandledMessages(): iterable
    {
        yield TransferWasAdded::class => [
            'method' => 'handleTransferWasAdded',
            'bus' => 'public.event.bus',
        ];
    }

    public function handleTransferWasAdded(TransferWasAdded $event): void
    {
        $this->jobBus->dispatch(
            new ProcessLeaderboardTransferJob(
                $event->storeId,
                $event->transferId,
                new \DateTimeImmutable()
            )
        );
    }
}