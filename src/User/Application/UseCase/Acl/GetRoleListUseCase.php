<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Application\UseCase\Acl;

use OpenLoyalty\Core\Domain\Search\Criteria\CriteriaInterface;
use OpenLoyalty\Core\Domain\Search\Criteria\TextCriteria;
use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponderInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponse;
use OpenLoyalty\User\Infrastructure\Entity\Repository\RoleRepositoryInterface;

class GetRoleListUseCase
{
    private RoleRepositoryInterface $roleRepository;
    private SearchableResponderInterface $searchableResponder;

    public function __construct(
        RoleRepositoryInterface $roleRepository,
        SearchableResponderInterface $searchableResponder
    ) {
        $this->roleRepository = $roleRepository;
        $this->searchableResponder = $searchableResponder;
    }

    public function execute(CriteriaCollectionInterface $criteriaCollection): SearchableResponse
    {
        $criteriaCollection->add((new TextCriteria('role', CriteriaInterface::EQUAL, 'ROLE_ADMIN'))
            ->setAsInternal());

        return $this->searchableResponder->fromCriteria($this->roleRepository, $criteriaCollection);
    }
}
