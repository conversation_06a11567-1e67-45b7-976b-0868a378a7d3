<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\User\Domain\Service;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\User\Domain\Model\Transaction;

interface TransactionAssignerInterface
{
    public function assignTransaction(
        CustomerId $customerId,
        Transaction $transaction,
        DateTimeImmutable $assignDate,
        bool $isSync = false
    ): void;
}
