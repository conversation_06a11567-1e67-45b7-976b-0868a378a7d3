<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Unit\Infrastructure\Security\Voter;

use OpenLoyalty\Test\Core\Integration\Infrastructure\BaseVoterTest;
use OpenLoyalty\User\Infrastructure\Entity\Role;
use OpenLoyalty\User\Infrastructure\Entity\User;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionCheckerInterface;
use OpenLoyalty\User\Infrastructure\Security\UserPermissionEvaluator;
use OpenLoyalty\User\Infrastructure\Security\Voter\AclVoter;
use PHPUnit\Framework\MockObject\MockObject;

final class AclVoterTest extends BaseVoterTest
{
    /**
     * @test
     */
    public function it_works(): void
    {
        /** @var UserPermissionCheckerInterface&MockObject $permissionChecker */
        $permissionChecker = $this->getMockBuilder(UserPermissionCheckerInterface::class)->getMock();
        $permissionChecker->method('hasPermissionWithoutStore')->willReturnCallback(function (User $user, string $resource, array $accesses) {
            return (new UserPermissionEvaluator())->hasPermission(null, $user, $resource, $accesses);
        });

        $attributes = [
            AclVoter::VIEW => ['customer' => false, 'admin' => true, 'admin_reporter' => true, 'id' => '1'],
            AclVoter::EDIT => ['customer' => false, 'admin' => true, 'admin_reporter' => false, 'id' => '1'],
            AclVoter::CREATE_ROLE => ['customer' => false, 'admin' => true, 'admin_reporter' => false, 'id' => ''],
            AclVoter::LIST => ['customer' => false, 'admin' => true, 'admin_reporter' => true, 'id' => ''],
        ];

        $voter = new AclVoter($permissionChecker);

        $this->assertVoterAttributes($voter, $attributes);
    }

    /**
     * {@inheritdoc}
     */
    protected function getSubjectById($id)
    {
        $role = $this->getMockBuilder(Role::class)->disableOriginalConstructor()->getMock();
        $role->method('getId')->willReturn(1);

        return $role;
    }
}
