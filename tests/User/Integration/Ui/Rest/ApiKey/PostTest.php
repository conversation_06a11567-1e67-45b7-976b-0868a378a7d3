<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\User\Integration\Ui\Rest\ApiKey;

use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\Test\Core\Integration\Traits\TenantApiTrait;
use OpenLoyalty\User\Infrastructure\DataFixtures\ORM\LoadAdminData;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class PostTest extends AbstractApiTest
{
    use TenantApiTrait;

    private HttpKernelBrowser $client;

    public function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }

    /**
     * @test
     */
    public function it_add_new_api_key(): void
    {
        $this->client->jsonRequest(
            'POST',
            '/api/admin/'.LoadAdminData::ADMIN_EXTERNAL_ID.'/api-key',
            [
                'apiKey' => [
                    'name' => 'Personal token',
                ],
            ]
        );

        $this->assertOkResponseStatus($this->client->getResponse());

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertArrayHasKey('apiKeyId', $data);
        $this->assertArrayHasKey('token', $data);
        $this->assertEquals(24, strlen($data['token']));
    }

    /**
     * @test
     */
    public function it_add_new_api_key_with_expiration_date(): void
    {
        $this->client->jsonRequest(
            'POST',
            '/api/admin/'.LoadAdminData::ADMIN_EXTERNAL_ID.'/api-key',
            [
                'apiKey' => [
                    'name' => 'Personal token',
                    'expirationDate' => '2222-12-12T00:00:00+00:00',
                ],
            ]
        );

        $this->assertOkResponseStatus($this->client->getResponse());

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('apiKeyId', $data);
        $this->assertArrayHasKey('token', $data);
    }

    /**
     * @test
     */
    public function it_try_to_add_new_api_key_with_invalid_expiration_date(): void
    {
        $this->client->jsonRequest(
            'POST',
            '/api/admin/'.LoadAdminData::ADMIN_EXTERNAL_ID.'/api-key',
            [
                'apiKey' => [
                    'name' => 'Personal token',
                    'expirationDate' => '1111-12-12T00:00:00+00:00',
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('errors', $data);
        $this->assertCount(1, $data['errors']);
        $this->assertArrayHasKey('message', $data['errors'][0]);
        $this->assertStringContainsString('This value should be greater than', $data['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_try_to_add_new_api_key_with_invalid_expiration_date_format(): void
    {
        $this->client->jsonRequest(
            'POST',
            '/api/admin/'.LoadAdminData::ADMIN_EXTERNAL_ID.'/api-key',
            [
                'apiKey' => [
                    'name' => 'Personal token',
                    'expirationDate' => '11.12.2023',
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('errors', $data);
        $this->assertCount(1, $data['errors']);
        $this->assertArrayHasKey('message', $data['errors'][0]);
        $this->assertStringContainsString("The value of 'Expiration date' is not a valid date.", $data['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_add_new_api_key_with_to_log_name(): void
    {
        $longName = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. '
            .'Etiam viverra orci nec lacinia dictum. Nunc tempor a felis ut tincidunt. '
            .'Fusce quis quam arcu. Donec vel mauris et ligula euismod semper nec et odio. '
            .'Etiam auctor ligula quis neque tristique, eget viverra nunc lobortis.';
        $this->client->jsonRequest(
            'POST',
            '/api/admin/'.LoadAdminData::ADMIN_EXTERNAL_ID.'/api-key',
            [
                'apiKey' => [
                    'name' => $longName,
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('errors', $data);
        $this->assertCount(1, $data['errors']);
        $this->assertArrayHasKey('message', $data['errors'][0]);
        $this->assertStringContainsString('This value is too long. It should have 255 characters or less', $data['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_try_to_add_api_key_above_the_limit(): void
    {
        $this->client->request(
            'GET',
            '/api/admin/'.LoadAdminData::ADMIN_EXTERNAL_ID.'/api-key',
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $currentlyUsed = $data['total']['all'];

        for ($i = 0; $i < 5 - $currentlyUsed; ++$i) {
            $this->client->jsonRequest(
                'POST',
                '/api/admin/'.LoadAdminData::ADMIN_EXTERNAL_ID.'/api-key',
                [
                    'apiKey' => [
                        'name' => 'Personal token'.$i,
                    ],
                ]
            );
            $this->assertOkResponseStatus($this->client->getResponse());
        }

        $this->client->jsonRequest(
            'POST',
            '/api/admin/'.LoadAdminData::ADMIN_EXTERNAL_ID.'/api-key',
            [
                'apiKey' => [
                    'name' => 'Personal token above the limit',
                ],
            ]
        );
        $response = $this->client->getResponse();
        $this->assertBadRequestResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('errors', $data);
        $this->assertCount(1, $data['errors']);
        $this->assertArrayHasKey('message', $data['errors'][0]);
        $this->assertStringContainsString('You can add a maximum of 5 api keys', $data['errors'][0]['message']);
    }

    /**
     * @test
     */
    public function it_try_to_add_new_api_key_with_invalid_admin_id(): void
    {
        $this->client->jsonRequest(
            'POST',
            '/api/admin/00000000-0000-0000-0000-000000000000/api-key',
            [
                'apiKey' => [
                    'name' => 'Personal token',
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertNotFoundResponseStatus($response);
  }
}
