<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Import\Unit\Infrastructure\Validator;

use OpenLoyalty\Import\Domain\Validator\XMLNodeValidatorInterface;
use OpenLoyalty\Import\Infrastructure\Validator\XMLNodeValidator;
use OpenLoyalty\Test\Common\BaseTestCase;
use SimpleXMLElement;

final class XmlNodeValidatorTest extends BaseTestCase
{
    /**
     * @test
     */
    public function it_validate_required_success(): void
    {
        $node = new SimpleXMLElement('<transaction><item>Value</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate($node, 'item', ['required' => true]);

        $this->assertNull($result);
    }

    /**
     * @test
     */
    public function it_validate_required_with_white_spaces(): void
    {
        $node = new SimpleXMLElement('<transaction><item>         </item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate($node, 'item', ['required' => true]);

        $this->assertSame('item should have value', $result);
    }

    /**
     * @test
     */
    public function it_validate_required_sub_items(): void
    {
        $node = new SimpleXMLElement('<transaction><items></items></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate($node, 'items', ['required' => true]);

        $this->assertSame('items should have value', $result);
    }

    /**
     * @test
     */
    public function it_validate_not_required_failed(): void
    {
        $node = new SimpleXMLElement('<transaction><item2>Value</item2></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate($node, 'item', ['required' => true]);

        $this->assertSame('item is required node', $result);
    }

    /**
     * @test
     */
    public function it_validate_date_time_format_success(): void
    {
        $node = new SimpleXMLElement('<transaction><item>2005-08-15T15:52:01+00:00</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            ['required' => true, 'format' => XMLNodeValidatorInterface::DATE_TIME_FORMAT]
        );

        $this->assertNull($result);
    }

    /**
     * @test
     */
    public function it_validate_date_time_format_failed(): void
    {
        $node = new SimpleXMLElement('<transaction><item>2005-08-15</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            ['required' => true, 'format' => XMLNodeValidatorInterface::DATE_TIME_FORMAT]
        );

        $this->assertSame('item has invalid date format (ATOM required)', $result);
    }

    /**
     * @test
     */
    public function it_validate_decimal_format_success(): void
    {
        $node = new SimpleXMLElement('<transaction><item>233.55</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            ['required' => true, 'format' => XMLNodeValidatorInterface::DECIMAL_FORMAT]
        );

        $this->assertNull($result);
    }

    /**
     * @test
     */
    public function it_validate_decimal_format_failed(): void
    {
        $node = new SimpleXMLElement('<transaction><item>445,33</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            ['required' => true, 'format' => XMLNodeValidatorInterface::DECIMAL_FORMAT]
        );

        $this->assertSame('item should be number value', $result);
    }

    /**
     * @test
     */
    public function it_validate_integer_format_success(): void
    {
        $node = new SimpleXMLElement('<transaction><item>44</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            ['required' => true, 'format' => XMLNodeValidatorInterface::INTEGER_FORMAT]
        );

        $this->assertNull($result);
    }

    /**
     * @test
     */
    public function it_validate_integer_format_failed(): void
    {
        $node = new SimpleXMLElement('<transaction><item>445.44</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            ['required' => true, 'format' => XMLNodeValidatorInterface::INTEGER_FORMAT]
        );

        $this->assertSame('item should be integer value', $result);
    }

    /**
     * @test
     */
    public function it_validate_required_field(): void
    {
        $node = new SimpleXMLElement('<transaction><item></item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            ['required' => true]
        );

        $this->assertSame('item should have value', $result);
    }

    /**
     * @test
     */
    public function it_validate_valid_const_format_success(): void
    {
        $node = new SimpleXMLElement('<transaction><item>const_1</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            [
                'required' => true,
                'format' => XMLNodeValidatorInterface::VALID_CONST_FORMAT,
                'values' => ['const_1', 'const_2'],
            ]
        );

        $this->assertNull($result);
    }

    /**
     * @test
     */
    public function it_validate_valid_const_format_failed(): void
    {
        $node = new SimpleXMLElement('<transaction><item>const_3</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            [
                'required' => true,
                'format' => XMLNodeValidatorInterface::VALID_CONST_FORMAT,
                'values' => ['const_1', 'const_2'],
            ]
        );

        $this->assertSame('item should one of (const_1, const_2)', $result);
    }

    /**
     * @test
     */
    public function it_validate_valid_bool_format_success(): void
    {
        $node = new SimpleXMLElement('<transaction><item>true</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            [
                'required' => true,
                'format' => XMLNodeValidatorInterface::BOOL_FORMAT,
            ]
        );

        $this->assertNull($result);
    }

    /**
     * @test
     */
    public function it_validate_valid_bool_format_failed(): void
    {
        $node = new SimpleXMLElement('<transaction><item>true3</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            [
                'required' => true,
                'format' => XMLNodeValidatorInterface::BOOL_FORMAT,
            ]
        );

        $this->assertSame('item should one of (true, false)', $result);
    }

    /**
     * @test
     */
    public function it_validate_date_format_success(): void
    {
        $node = new SimpleXMLElement('<transaction><item>2005-08-15</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            ['required' => true, 'format' => XMLNodeValidatorInterface::DATE_FORMAT]
        );

        $this->assertNull($result);
    }

    /**
     * @test
     */
    public function it_validate_date_format_failed(): void
    {
        $node = new SimpleXMLElement('<transaction><item>2005-08-15T15:52:01+00:00</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            ['required' => true, 'format' => XMLNodeValidatorInterface::DATE_FORMAT]
        );

        $this->assertSame('item has invalid date format (Y-m-d required)', $result);
    }

    /**
     * @test
     */
    public function it_validate_uuid_format_success(): void
    {
        $node = new SimpleXMLElement('<transaction><item>000096cf-32a3-43bd-9034-4df343e5fd94</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            ['required' => true, 'format' => XMLNodeValidatorInterface::UUID_FORMAT]
        );

        $this->assertNull($result);
    }

    /**
     * @test
     */
    public function it_validate_uuid_format_failed(): void
    {
        $node = new SimpleXMLElement('<transaction><item>000096cf-4df343e5fd94</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            ['required' => true, 'format' => XMLNodeValidatorInterface::UUID_FORMAT]
        );

        $this->assertSame('item should be UUID', $result);
    }

    /**
     * @test
     */
    public function it_validate_email_format_success(): void
    {
        $node = new SimpleXMLElement('<transaction><item><EMAIL></item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            ['required' => true, 'format' => XMLNodeValidatorInterface::EMAIL_FORMAT]
        );

        $this->assertNull($result);
    }

    /**
     * @test
     */
    public function it_validate_email_format_failed(): void
    {
        $node = new SimpleXMLElement('<transaction><item>invalid_email</item></transaction>');

        $nodeValidator = new XMLNodeValidator();
        $result = $nodeValidator->validate(
            $node,
            'item',
            ['required' => true, 'format' => XMLNodeValidatorInterface::EMAIL_FORMAT]
        );

        $this->assertSame('item is not valid email', $result);
    }
}
