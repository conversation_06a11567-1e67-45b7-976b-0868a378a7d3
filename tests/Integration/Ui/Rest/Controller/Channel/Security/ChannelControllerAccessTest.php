<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Integration\Ui\Rest\Controller\Channel\Security;

use OpenLoyalty\Channel\Infrastructure\DataFixtures\ORM\LoadChannelData;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Core\Integration\Infrastructure\BaseAccessControlTest;

final class ChannelControllerAccessTest extends BaseAccessControlTest
{
    public function testOnlyAdminShouldHaveAccessToAllChannelsList(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/channel');
    }

    public function testOnlyAdminCanEditChannel(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/channel/'.LoadChannelData::CHANNEL_ID, [], 'PUT');
    }

    public function testOnlyAdminCanCreateChannel(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/channel', [], 'POST');
    }

    public function testOnlyAdminCanViewChannel(): void
    {
        $clients = [
            ['client' => $this->getCustomerClient(), 'status' => 403, 'name' => 'customer'],
            ['client' => $this->getAdminClient(), 'not_status' => 403, 'name' => 'admin'],
        ];

        $this->checkClients($clients, '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/channel/'.LoadChannelData::CHANNEL_ID);
    }
}
