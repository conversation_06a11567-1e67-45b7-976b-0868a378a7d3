<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Segment\Integration\Ui\Rest;

use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

final class PutTest extends AbstractApiTest
{
    private HttpKernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = self::createAuthenticatedClient();
    }

    /**
     * @test
     */
    public function it_not_create_segment_with_invalid_creterion_id(): void
    {
        $tenantCode = 'invalid_criterion_uuid';
        $this->createTenant($tenantCode);

        $this->client->request(
            'POST',
            '/api/'.$tenantCode.'/segment',
            [
                'segment' => [
                    'name' => 'Test',
                    'active' => true,
                    'description' => 'Members with at least 1 transaction in range',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'type' => 'transaction_count_in_period',
                                    'fromDate' => '2000-06-01 18:00',
                                    'toDate' => '2024-06-01 12:00',
                                    'operator' => [
                                        'code' => 'is_greater_or_equal',
                                    ],
                                    'count' => 1,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);

        $segment = json_decode($response->getContent(), true);

        $this->client->request(
            'PUT',
            '/api/'.$tenantCode.'/segment/'.$segment['segmentId'],
            [
                'segment' => [
                    'name' => 'Test',
                    'active' => true,
                    'description' => 'Members with at least 1 transaction in range',
                    'parts' => [
                        [
                            'criteria' => [
                                [
                                    'criterionId' => 'invalid_uuid',
                                    'type' => 'transaction_count_in_period',
                                    'fromDate' => '2000-06-01 18:00',
                                    'toDate' => '2024-06-01 12:00',
                                    'operator' => [
                                        'code' => 'is_greater_or_equal',
                                    ],
                                    'count' => 1,
                                ],
                            ],
                        ],
                    ],
                ],
            ]
        );

        $response = $this->client->getResponse();
        $data = json_decode($response->getContent(), true);
        $errors = $data['errors'];

        $this->assertBadRequestResponseStatus($response);
        $this->assertStringContainsString('This value is not a valid UUID.', $errors[0]['message']);
    }
}
