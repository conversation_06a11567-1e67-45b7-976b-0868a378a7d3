<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Unit\Application\Messaging\UseCase;

use OpenLoyalty\Core\Domain\Search\CriteriaCollectionInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponderInterface;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableResponse;
use OpenLoyalty\Core\Domain\Search\Responder\SearchableTotalResponse;
use OpenLoyalty\Messaging\Application\DataMapper\WebhookSubscriptionDataMapper;
use OpenLoyalty\Messaging\Application\UseCase\GetWebhookSubscriptionsListUseCase;
use OpenLoyalty\Messaging\Domain\WebhookSubscriptionRepositoryInterface;
use OpenLoyalty\Test\Common\BaseTestCase;

final class GetWebhookSubscriptionsListUseCaseTest extends BaseTestCase
{
    /**
     * @test
     */
    public function it_returns_webhook_subscriptions_list(): void
    {
        $webhookSubscriptionRepository = $this->createMock(WebhookSubscriptionRepositoryInterface::class);
        $searchableResponder = $this->createMock(SearchableResponderInterface::class);
        $dataMapper = $this->createMock(WebhookSubscriptionDataMapper::class);

        $useCase = new GetWebhookSubscriptionsListUseCase(
            $webhookSubscriptionRepository,
            $searchableResponder,
            $dataMapper
        );

        $criteriaCollection = $this->createMock(CriteriaCollectionInterface::class);
        $searchableResponse = $this->createMock(SearchableResponse::class);
        $searchableResponse->method('getItems')->willReturn([]);
        $searchableTotalResponse = $this->createMock(SearchableTotalResponse::class);
        $searchableResponse->method('getTotal')->willReturn($searchableTotalResponse);
        $searchableResponder->method('fromCriteria')->willReturn($searchableResponse);
        $dataMapper->method('mapList')->willReturn([]);
        $searchableTotalResponse->method('getAll')->willReturn(5);
        $searchableTotalResponse->method('getFiltered')->willReturn(5);

        $this->assertEquals(
            new SearchableResponse([], 5, 5),
            $useCase->execute($criteriaCollection)
        );
    }
}
