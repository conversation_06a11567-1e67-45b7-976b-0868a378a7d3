<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Campaign\Integration\Ui\Rest;

use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;

final class InternalEventForCustomAttributesTest extends AbstractApiTest
{
    /**
     * @test
     */
    public function it_adds_points_from_internal_event_when_custom_attributes_changed(): void
    {
        $client = self::createAuthenticatedClient();

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                ],
            ]
        );

        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $memberId = $data['customerId'];

        $this->checkCustomerStatus(
            $memberId,
            [
                'activePoints' => 0.0,
                'earnedPoints' => 0.0,
                'spentPoints' => 0.0,
            ]
        );

        $campaignData = [
            'campaign' => [
                'translations' => [
                    'en' => [
                        'name' => 'Custom attribute details updated',
                    ],
                ],
                'active' => true,
                'type' => 'direct',
                'trigger' => 'internal_event',
                'event' => 'MemberDetailsChanged',
                'activity' => [
                    'startsAt' => '2022-01-01 00:00:00',
                    'endsAt' => null,
                ],
                'rules' => [
                    [
                        'effects' => [
                            [
                                'effect' => 'give_points',
                                'pointsRule' => 10,
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $client->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/campaign',
            $campaignData
        );
        $response = $client->getResponse();
        $this->assertOkResponseStatus($response);

        $client->request(
            'PUT',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$memberId.'/custom-attribute',
            [
                'customAttributes' => [
                    [
                        'key' => 'a',
                        'value' => 'b',
                    ],
                ],
            ]
        );
        $this->assertNoContentResponseStatus($client->getResponse());

        $this->checkCustomerStatus(
            $memberId,
            [
                'activePoints' => 10.0,
                'earnedPoints' => 10.0,
                'spentPoints' => 0.0,
            ]
        );
    }
}
