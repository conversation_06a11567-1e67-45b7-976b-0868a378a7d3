<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Test\Reward\Integration\Ui;

use DateTime;
use OpenLoyalty\Core\Domain\Id\CustomerId;
use OpenLoyalty\Core\Domain\Id\RewardId;
use OpenLoyalty\Core\Domain\Model\Label;
use OpenLoyalty\Integration\Infrastructure\Utility\Traits\UploadedFileTrait;
use OpenLoyalty\Level\Infrastructure\DataFixtures\ORM\LoadLevelData;
use OpenLoyalty\Reward\Domain\MaterialReward;
use OpenLoyalty\Reward\Domain\Repository\RewardRepository;
use OpenLoyalty\Reward\Domain\Reward;
use OpenLoyalty\Reward\Domain\StaticCouponReward;
use OpenLoyalty\Reward\Infrastructure\DataFixtures\ORM\LoadRewardData;
use OpenLoyalty\Settings\Infrastructure\DataFixtures\ORM\LoadSettingsData;
use OpenLoyalty\Test\Common\Integration\AbstractApiTest;
use OpenLoyalty\User\Domain\Customer;
use OpenLoyalty\User\Domain\CustomerRepositoryInterface;
use OpenLoyalty\User\Infrastructure\DataFixtures\ORM\LoadUserData;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\HttpKernelBrowser;

/**
 * @covers \OpenLoyalty\Reward\Ui\Rest\Controller\BrandIcon\Post
 * @covers \OpenLoyalty\Reward\Ui\Rest\Controller\BrandIcon\Get
 * @covers \OpenLoyalty\Reward\Ui\Rest\Controller\BrandIcon\Delete
 * @covers \OpenLoyalty\Reward\Ui\Rest\Controller\Post
 * @covers \OpenLoyalty\Reward\Ui\Rest\Controller\Put
 * @covers \OpenLoyalty\Reward\Ui\Rest\Controller\Get
 * @covers \OpenLoyalty\Reward\Ui\Rest\Controller\GetList
 * @covers \OpenLoyalty\Reward\Ui\Rest\Controller\Redemption\GetExport
 * @covers \OpenLoyalty\Reward\Ui\Rest\Controller\GetPublic
 * @covers \OpenLoyalty\Reward\Ui\Rest\Controller\PostBuy
 * @covers \OpenLoyalty\Reward\Ui\Rest\Controller\Member\GetList
 * @covers \OpenLoyalty\Reward\Ui\Rest\Controller\Photo\Post
 * @covers \OpenLoyalty\Reward\Ui\Rest\Controller\Photo\Get
 */
final class RewardControllerTest extends AbstractApiTest
{
    use UploadedFileTrait;

    public const EMPTY_CVS_EXPORT_FILE_LENGTH = 185;

    private HttpKernelBrowser $client;
    private HttpKernelBrowser $authenticatedClient;
    private RewardRepository $rewardRepository;
    private CustomerRepositoryInterface $customerRepository;

    /**
     * @test
     */
    public function it_updates_reward_brand(): void
    {
        $imgContent = file_get_contents(__DIR__.'/../Resources/fixtures/test.jpg');

        $this->authenticatedClient->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::REWARD2_ID.'/brand_icon',
            [],
            [
                'brand_icon' => [
                    'file' => $this->createUploadedFile($imgContent, 'test.jpg', 'image/jpeg', UPLOAD_ERR_OK),
                ],
            ]
        );

        $this->assertNoContentResponseStatus($this->authenticatedClient->getResponse());
    }

    /**
     * @test
     */
    public function it_returns_reward_brand(): void
    {
        $imgContent = file_get_contents(__DIR__.'/../Resources/fixtures/test.jpg');

        $this->authenticatedClient->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::REWARD2_ID.'/brand_icon',
            [],
            [
                'brand_icon' => [
                    'file' => $this->createUploadedFile($imgContent, 'test.jpg', 'image/jpeg', UPLOAD_ERR_OK),
                ],
            ]
        );

        $response = $this->authenticatedClient->getResponse();
        $this->assertNoContentResponseStatus($response);

        $fileHash = md5_file(__DIR__.'/../Resources/fixtures/test.jpg');

        $this->authenticatedClient->request('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::REWARD2_ID.'/brand_icon');

        $response = $this->authenticatedClient->getResponse();

        $this->assertSame($fileHash, md5($response->getContent()), 'File has not been uploaded correctly.');
    }

    /**
     * @test
     */
    public function it_removes_reward_brand(): void
    {
        $this->authenticatedClient->request(
            'DELETE',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::REWARD2_ID.'/brand_icon',
        );

        $this->assertNoContentResponseStatus($this->authenticatedClient->getResponse());
    }

    /**
     * @test
     */
    public function it_creates_reward(): void
    {
        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'reward' => StaticCouponReward::TYPE,
                    'levels' => [LoadLevelData::LEVEL2_ID],
                    'segments' => [],
                    'target' => 'level',
                    'usageLimit' => [
                        'perUser' => 2,
                    ],
                    'costInPoints' => 12,
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'daysValid' => 1,
                    'daysInactive' => 1,
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'labels' => [
                        ['key' => 'key0', 'value' => 'value0'],
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                    'taxPriceValue' => 99.95,
                    'tax' => 23.5,
                    'couponValue' => 12,
                    'translations' => [
                        'en' => [
                            'name' => 'test',
                            'shortDescription' => 'short description',
                            'brandName' => 'Somebrand EN',
                        ],
                    ],
                ],
            ]
        );

        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('rewardId', $data);

        $reward = $this->rewardRepository->byId(new RewardId($data['rewardId']));

        $this->assertInstanceOf(Reward::class, $reward);
        $this->assertSame(99.95, $reward->getTaxPriceValue());
        $this->assertSame(0.235, $reward->getTax());
        $this->assertIsArray($reward->getLabels());
        $this->assertCount(2, $reward->getLabels());
        foreach ($reward->getLabels() as $key => $label) {
            $this->assertInstanceOf(Label::class, $label);
            $this->assertSame('key'.$key, $label->getKey());
            $this->assertSame('value'.$key, $label->getValue());
        }
    }

    /**
     * @test
     */
    public function it_does_not_create_reward_when_value_is_too_big(): void
    {
        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'reward' => StaticCouponReward::TYPE,
                    'levels' => [LoadLevelData::LEVEL2_ID],
                    'segments' => [],
                    'target' => 'level',
                    'usageLimit' => [
                        'perUser' => 2,
                    ],
                    'costInPoints' => 12,
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'daysValid' => 2147483647,
                    'daysInactive' => 2147483647,
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'labels' => [
                        ['key' => 'key0', 'value' => 'value0'],
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                    'taxPriceValue' => 99.95,
                    'tax' => 23.5,
                    'couponValue' => 12,
                    'translations' => [
                        'en' => [
                            'name' => 'test',
                            'shortDescription' => 'short description',
                            'brandName' => 'Somebrand EN',
                        ],
                    ],
                ],
            ]
        );

        $response = $this->authenticatedClient->getResponse();

        $this->assertBadRequestResponseStatus($response);
        self::assertStringContainsString('This value should be between 1 and 2147483646', $response->getContent());
        self::assertStringContainsString('daysValid', $response->getContent());
        self::assertStringContainsString('daysInactive', $response->getContent());
    }

    /**
     * @test
     */
    public function it_does_not_create_reward_when_name_is_too_long(): void
    {
        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'reward' => StaticCouponReward::TYPE,
                    'levels' => [],
                    'segments' => [],
                    'usageLimit' => [
                        'perUser' => 2,
                    ],
                    'costInPoints' => 12,
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'daysValid' => 10,
                    'daysInactive' => 5,
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'labels' => [
                        ['key' => 'key0', 'value' => 'value0'],
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                    'taxPriceValue' => 99.95,
                    'tax' => 23.5,
                    'couponValue' => 12,
                    'translations' => [
                        'en' => [
                            'name' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in.',
                            'shortDescription' => 'short description',
                            'brandName' => 'Somebrand EN',
                        ],
                    ],
                ],
            ]
        );

        $response = $this->authenticatedClient->getResponse();

        $this->assertBadRequestResponseStatus($response);
        self::assertStringContainsString('This value is too long. It should have 255 characters or less.', $response->getContent());
        self::assertStringContainsString('translations.en.name', $response->getContent());
    }

    /**
     * @test
     */
    public function it_does_not_creates_reward_when_generated_coupon_length_is_not_valid(): void
    {
        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'reward' => StaticCouponReward::TYPE,
                    'levels' => [LoadLevelData::LEVEL2_ID],
                    'segments' => [],
                    'target' => 'level',
                    'usageLimit' => [
                        'perUser' => 2,
                    ],
                    'costInPoints' => 12,
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'daysValid' => 32,
                    'daysInactive' => 323,
                    'couponGenerator' => [
                        'length' => 100000000000,
                        'characterSet' => 'alphanum',
                    ],
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'labels' => [
                        ['key' => 'key0', 'value' => 'value0'],
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                    'taxPriceValue' => 99.95,
                    'tax' => 23.5,
                    'couponValue' => 12,
                    'translations' => [
                        'en' => [
                            'name' => 'test',
                            'shortDescription' => 'short description',
                            'brandName' => 'Somebrand EN',
                        ],
                    ],
                ],
            ]
        );

        $response = $this->authenticatedClient->getResponse();

        $this->assertBadRequestResponseStatus($response);
        self::assertStringContainsString('This value should be less than or equal to 32', $response->getContent());
        self::assertStringContainsString('couponGenerator', $response->getContent());
    }

    /**
     * @test
     */
    public function it_return_tax_in_correct_format(): void
    {
        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'reward' => StaticCouponReward::TYPE,
                    'levels' => [LoadLevelData::LEVEL2_ID],
                    'segments' => [],
                    'target' => 'level',
                    'usageLimit' => [
                        'perUser' => 2,
                    ],
                    'costInPoints' => 12.123,
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'daysValid' => 1,
                    'daysInactive' => 1,
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'labels' => [
                        ['key' => 'key0', 'value' => 'value0'],
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                    'taxPriceValue' => 99.95,
                    'tax' => 25.5,
                    'couponValue' => 12,
                    'translations' => [
                        'en' => [
                            'name' => 'tax',
                            'shortDescription' => 'short description',
                            'brandName' => 'Somebrand EN',
                        ],
                    ],
                ],
            ]
        );

        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('rewardId', $data);

        $this->authenticatedClient->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$data['rewardId']
        );

        $response = $this->authenticatedClient->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->assertSame(99.95, $data['taxPriceValue']);
        $this->assertSame(25.5, $data['tax']);
        $this->assertSame(12.123, $data['costInPoints']);
        $this->assertSame(12.0, $data['couponValue']);
        $this->assertArrayNotHasKey('unitsConversion', $data);
    }

    /**
     * @test
     */
    public function it_does_not_create_reward_when_inactive_or_valid_days_are_equal_to_zero(): void
    {
        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'reward' => StaticCouponReward::TYPE,
                    'levels' => [LoadLevelData::LEVEL2_ID],
                    'segments' => [],
                    'target' => 'level',
                    'usageLimit' => [
                        'perUser' => 2,
                    ],
                    'costInPoints' => 12,
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'daysValid' => 0,
                    'daysInactive' => 0,
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'labels' => [
                        ['key' => 'key0', 'value' => 'value0'],
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                    'taxPriceValue' => 99.95,
                    'tax' => 23.5,
                    'couponValue' => 12,
                    'translations' => [
                        'en' => [
                            'name' => 'test',
                            'shortDescription' => 'short description',
                            'brandName' => 'Somebrand EN',
                        ],
                    ],
                ],
            ]
        );

        $response = $this->authenticatedClient->getResponse();
        $this->assertBadRequestResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_does_not_creates_reward_with_too_high_costInPoints(): void
    {
        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'reward' => StaticCouponReward::TYPE,
                    'levels' => [LoadLevelData::LEVEL2_ID],
                    'segments' => [],
                    'target' => 'level',
                    'usageLimit' => [
                        'perUser' => 2,
                    ],
                    'costInPoints' => 1222222222222,
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'daysValid' => 1,
                    'daysInactive' => 1,
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'labels' => [
                        ['key' => 'key0', 'value' => 'value0'],
                        ['key' => 'key1', 'value' => 'value1'],
                    ],
                    'taxPriceValue' => 99.95,
                    'tax' => 23,
                    'couponValue' => 12,
                    'translations' => [
                        'en' => [
                            'name' => 'test',
                            'shortDescription' => 'short description',
                            'brandName' => 'Somebrand EN',
                        ],
                    ],
                ],
            ]
        );

        $this->assertBadRequestResponseStatus($this->authenticatedClient->getResponse());
    }

    /**
     * @test
     */
    public function it_creates_material_reward(): void
    {
        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'reward' => MaterialReward::TYPE,
                    'levels' => [LoadLevelData::LEVEL2_ID],
                    'segments' => [],
                    'target' => 'level',
                    'usageLimit' => [
                        'general' => 10,
                        'perUser' => 2,
                    ],
                    'costInPoints' => 12,
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'translations' => [
                        'en' => [
                            'name' => 'test_single_coupon',
                            'shortDescription' => 'short description',
                        ],
                    ],
                ],
            ]
        );

        $response = $this->authenticatedClient->getResponse();
        $this->assertOkResponseStatus($response);
    }

    /**
     * @test
     */
    public function it_updates_reward(): void
    {
        $this->authenticatedClient->jsonRequest(
            'PUT',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::REWARD2_ID,
            [
                'reward' => [
                    'levels' => [LoadLevelData::LEVEL2_ID],
                    'segments' => [],
                    'active' => true,
                    'costInPoints' => 5,
                    'target' => 'level',
                    'couponGenerator' => [
                        'length' => 9,
                        'characterSet' => 'alphanum',
                    ],
                    'usageLimit' => [
                        'general' => 300,
                        'perUser' => 400,
                    ],
                    'daysValid' => 1,
                    'daysInactive' => 1,
                    'labels' => [
                        ['key' => 'type', 'value' => 'promotion'],
                    ],
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'taxPriceValue' => 300.95,
                    'tax' => 23,
                    'couponValue' => 12,
                    'translations' => [
                        'en' => [
                            'name' => 'test',
                            'shortDescription' => 'short description',
                            'brandName' => 'Somebrand EN',
                        ],
                    ],
                ],
            ]
        );

        $this->assertNoContentResponseStatus($this->authenticatedClient->getResponse());
        $reward = $this->rewardRepository->byId(new RewardId(LoadRewardData::REWARD2_ID));

        $this->assertInstanceOf(Reward::class, $reward);
        $this->assertSame('test', $reward->getName());
        $this->assertSame(300.95, $reward->getTaxPriceValue());
        $this->assertSame(0.23, $reward->getTax());
        $this->assertIsArray($reward->getLabels());
        $this->assertCount(1, $reward->getLabels());

        $label = $reward->getLabels()[0];

        $this->assertInstanceOf(Label::class, $label);
        $this->assertSame('type', $label->getKey());
        $this->assertSame('promotion', $label->getValue());
        $this->assertSame('test', $reward->getName());
        $this->assertSame('short description', $reward->getShortDescription());
    }

    /**
     * @test
     * @dataProvider getTaxValues
     */
    public function it_updates_reward_with_tax_values(float $tax, bool $expectedSuccess): void
    {
        $this->authenticatedClient->jsonRequest(
            'PUT',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::REWARD2_ID,
            [
                'reward' => [
                    'levels' => [LoadLevelData::LEVEL2_ID],
                    'segments' => [],
                    'active' => true,
                    'costInPoints' => 5,
                    'target' => 'level',
                    'couponGenerator' => [
                        'length' => 9,
                        'characterSet' => 'alphanum',
                    ],
                    'usageLimit' => [
                        'general' => 300,
                        'perUser' => 400,
                    ],
                    'daysValid' => 1,
                    'daysInactive' => 1,
                    'labels' => [
                        ['key' => 'type', 'value' => 'promotion'],
                    ],
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'taxPriceValue' => 300.95,
                    'tax' => $tax,
                    'couponValue' => 12,
                    'translations' => [
                        'en' => [
                            'name' => 'test',
                            'shortDescription' => 'short description',
                            'brandName' => 'Somebrand EN',
                        ],
                    ],
                ],
            ],
            $this->addDisableOpenApiValidationHeader()
        );

        $response = $this->authenticatedClient->getResponse();

        if (true === $expectedSuccess) {
            $this->assertNoContentResponseStatus($response);
            $reward = $this->rewardRepository->byId(new RewardId(LoadRewardData::REWARD2_ID));
            $this->assertSame($tax / 100, $reward->getTax());
        } else {
            $this->assertBadRequestResponseStatus($response);
        }
    }

    public function getTaxValues(): array
    {
        return [
            [0, true],
            [1.5, true],
            [99, true],
            [101, false],
            [101.5, false],
            [23, true],
        ];
    }

    /**
     * @test
     */
    public function it_validates_form(): void
    {
        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'levels' => [LoadLevelData::LEVEL2_ID],
                    'segments' => [],
                    'daysValid' => 1,
                    'daysInactive' => 1,
                    'activity' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-01-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-01-11'))->format(\DateTimeInterface::ATOM),
                    ],
                    'visibility' => [
                        'allTime' => false,
                        'from' => (new DateTime('2016-02-01'))->format(\DateTimeInterface::ATOM),
                        'to' => (new DateTime('2037-02-11'))->format(\DateTimeInterface::ATOM),
                    ],
                ],
            ],
            $this->addDisableOpenApiValidationHeader()
        );

        $this->assertBadRequestResponseStatus($this->authenticatedClient->getResponse());
    }

    /**
     * @test
     */
    public function it_returns_rewards_list(): void
    {
        $this->authenticatedClient->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
        );

        $response = $this->authenticatedClient->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('items', $data);
        foreach ($data['items'] as $item) {
            $this->assertArrayHasKey('reward', $item);
            if ('conversion_coupon' !== $item['reward']) {
                $this->assertArrayNotHasKey('unitsConversion', $item);
            } else {
                $this->assertArrayHasKey('unitsConversion', $item);
            }
        }
        $this->assertNotEmpty($data['items'], 'Contains at least one element');
    }

    /**
     * @test
     *
     * @dataProvider getRewardsFilters
     */
    public function it_filters_rewards_list(array $filters, int $expectedCount): void
    {
        $filters['_itemsOnPage'] = 50;

        $this->authenticatedClient->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            $filters
        );

        $response = $this->authenticatedClient->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('items', $data);
        $this->assertArrayHasKey('total', $data);
        $this->assertCount($expectedCount, $data['items']);
        $this->assertSame($expectedCount, $data['total']['filtered']);
    }

    /**
     * @test
     */
    public function it_filters_rewards_by_non_existent_not_valid_uuid(): void
    {
        $this->authenticatedClient->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'rewardId' => 'not-valid-uuid',
            ],
            [],
            $this->addDisableOpenApiValidationHeader()
        );

        $response = $this->authenticatedClient->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('items', $data);
        $this->assertArrayHasKey('total', $data);
        $this->assertSame(0, $data['total']['filtered']);
        $this->assertGreaterThan(0, $data['total']['all']);
    }

    /**
     * @test
     * @dataProvider sortParamsProvider
     */
    public function it_returns_rewards_list_sorted(string $field, string $direction, string $oppositeDirection): void
    {
        $this->authenticatedClient->request('GET', sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward?_orderBy[%s]=%s', $field, $direction));

        $sortedResponse = $this->authenticatedClient->getResponse();
        $sortedData = json_decode($sortedResponse->getContent(), true);

        $this->assertOkResponseStatus($sortedResponse);
        $this->assertArrayHasKey('items', $sortedData);

        $firstElementSorted = reset($sortedData['items']);
        $sortedSize = count($sortedData['items']);

        if ($sortedData['total']['all'] < 2) {
            return;
        }

        $this->authenticatedClient->request('GET', sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward?_orderBy[%s]=%s', $field, $oppositeDirection));

        $oppositeSortedResponse = $this->authenticatedClient->getResponse();
        $oppositeSortedData = json_decode($oppositeSortedResponse->getContent(), true);

        $firstElement = reset($oppositeSortedData['items']);
        $size = count($oppositeSortedData['items']);

        $this->assertNotEquals($firstElement['rewardId'], $firstElementSorted['rewardId']);
        $this->assertSame($size, $sortedSize);
    }

    /**
     * @test
     */
    public function it_returns_bought_rewards_list(): void
    {
        // todo add some bought rewards and check items quantity
        $this->authenticatedClient->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/redemption',
        );
        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('items', $data);
    }

    /**
     * @test
     */
    public function it_returns_bought_rewards_list_filtered_by_future_date_from(): void
    {
        //todo add some bought rewards wit redemption date equal or greater than now
        $this->authenticatedClient->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/redemption?redemptionDate[gte]='.date('Y-m-d H:i:s'),
        );
        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('items', $data);
        $this->assertArrayHasKey('total', $data);
        $this->assertSame(0, $data['total']['filtered']);
    }

    /**
     * @test
     */
    public function it_returns_bought_rewards_list_filtered_by_a_filter_with_default_operator(): void
    {
        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'agreement1' => true,
                ],
            ]
        );
        $response = $this->authenticatedClient->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $this->authenticatedClient->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/points/add',
            [
                'transfer' => [
                    'customer' => $customerId,
                    'points' => 1,
                ],
            ]
        );
        $this->assertOkResponseStatus($this->authenticatedClient->getResponse());

        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Bought reward',
                        ],
                    ],
                    'reward' => 'conversion_coupon',
                    'active' => true,
                    'activity' => [
                        'allTime' => true,
                    ],
                    'couponGenerator' => [
                        'characterSet' => 'alphanum',
                        'length' => 5,
                    ],
                    'usageLimit' => [
                        'general' => 100,
                        'perUser' => 100,
                    ],
                    'visibility' => [
                        'allTime' => true,
                    ],
                    'featured' => true,
                    'public' => true,
                    'unitsConversion' => [
                        'ratio' => 1.3,
                        'rounding' => 'default',
                    ],
                ],
            ]
        );
        $response = $this->authenticatedClient->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $rewardId = $data['rewardId'];

        $this->authenticatedClient->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$rewardId.'/buy',
            [
                'customerId' => $customerId,
                'units' => 1,
            ]
        );
        $this->assertOkResponseStatus($this->authenticatedClient->getResponse());

        $this->authenticatedClient->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/redemption',
        );
        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('items', $data);
        $this->assertArrayHasKey('total', $data);
        $this->assertSame(1, $data['total']['filtered']);
    }

    /**
     * @test
     */
    public function it_returns_reward(): void
    {
        $this->authenticatedClient->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::REWARD_ID,
        );
        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('rewardId', $data);
        $this->assertArrayHasKey('levels', $data);
        $this->assertIsArray($data['levels']);
        $this->assertArrayHasKey('segments', $data);
        $this->assertIsArray($data['segments']);
        $this->assertArrayHasKey('name', $data);
        $this->assertIsString($data['name']);
        $this->assertArrayHasKey('active', $data);
        $this->assertIsBool($data['active']);
        $this->assertArrayHasKey('costInPoints', $data);
        $this->assertIsFloat($data['costInPoints']);
        $this->assertArrayHasKey('segmentNames', $data);
        $this->assertIsArray($data['segmentNames']);
        $this->assertArrayHasKey('levelNames', $data);
        $this->assertIsArray($data['levelNames']);
        $this->assertArrayHasKey('usageLeft', $data);
        $this->assertIsInt($data['usageLeft']);
        $this->assertSame(LoadRewardData::REWARD_ID, $data['rewardId']);
        $this->assertIsArray($data['labels']);
        $this->assertCount(1, $data['labels']);
        $this->assertArrayHasKey('key', $data['labels'][0]);
        $this->assertArrayHasKey('value', $data['labels'][0]);

        //translations
        //en
        $this->assertCount(2, $data['translations']);
        $this->assertArrayHasKey('name', $data['translations'][0]);
        $this->assertArrayHasKey('shortDescription', $data['translations'][0]);
        $this->assertArrayHasKey('locale', $data['translations'][0]);
        $this->assertSame('Test configured reward', $data['translations'][0]['name']);
        $this->assertSame('Some _Reward_ short description', $data['translations'][0]['shortDescription']);
        $this->assertSame('Some _Brand_ description', $data['translations'][0]['brandDescription']);
        $this->assertSame('en', $data['translations'][0]['locale']);
        //pl
        $this->assertArrayHasKey('name', $data['translations'][1]);
        $this->assertArrayHasKey('shortDescription', $data['translations'][1]);
        $this->assertArrayHasKey('locale', $data['translations'][1]);
        $this->assertSame('Skonfigurowana testowa kampania', $data['translations'][1]['name']);
        $this->assertSame('Opis skonfigurowanej kampanii testowej', $data['translations'][1]['shortDescription']);
        $this->assertSame('pl', $data['translations'][1]['locale']);
    }

    /**
     * @test
     */
    public function it_returns_reward_using_html_format(): void
    {
        $this->authenticatedClient->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::REWARD_ID,
            [
                'format' => 'html',
            ]
        );
        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);
        $this->assertOkResponseStatus($response);

        $this->assertArrayHasKey('brandDescription', $data);
        $this->assertArrayHasKey('shortDescription', $data);
        $this->assertArrayHasKey('conditionsDescription', $data);
        $this->assertArrayHasKey('usageInstruction', $data);

        $this->assertSame('Some <em>Brand</em> description', $data['brandDescription']);
        $this->assertSame('Some <em>Reward</em> short description', $data['shortDescription']);
        $this->assertSame('Some <em>Reward</em> condition description', $data['conditionsDescription']);
        $this->assertSame('How to use coupon in this <em>reward</em>', $data['usageInstruction']);
    }

    /**
     * @test
     */
    public function it_returns_customer_available_rewards(): void
    {
        $customerDetails = $this->getCustomerDetails(LoadUserData::USER_USERNAME);
        $customerId = $customerDetails->getCustomerId();

        $this->authenticatedClient->request(
            'GET',
            sprintf('/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/%s/reward?available=true', $customerId)
        );

        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('items', $data);
        $this->assertNotEmpty($data['items']);
    }

    /**
     * @test
     */
    public function it_allows_to_buy_a_reward_for_customer(): void
    {
        $customerDetailsBefore = $this->getCustomerDetails(LoadUserData::USER_USERNAME);
        $accountBefore = $this->getDefaultAccount($customerDetailsBefore->getCustomerId());

        $this->authenticatedClient->request(
            'POST',
            sprintf(
                '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/%s/buy',
                LoadRewardData::REWARD2_ID
            ),
            [
                'customerId' => $customerDetailsBefore->getCustomerId(),
                'quantity' => 8,
            ]
        );

        $this->assertOkResponseStatus($this->authenticatedClient->getResponse());

        $accountAfter = $this->getDefaultAccount($customerDetailsBefore->getCustomerId());
        $amountBefore = $accountBefore ? $accountBefore['activePoints'] : 0;
        $amountAfter = $accountAfter ? $accountAfter['activePoints'] : 0;
        $this->assertEqualsWithDelta(
            $amountBefore - (8 * 5),
            $amountAfter,
            0.001,
            sprintf(
                'There should be %s points available after the reward is bought, but there are %s',
                $amountBefore - (8 * 5),
                $amountAfter
            )
        );
    }

    /**
     * @test
     */
    public function it_returns_csv_response_when_exports_bought_data(): void
    {
        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'agreement1' => true,
                ],
            ]
        );
        $response = $this->authenticatedClient->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $this->authenticatedClient->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/points/add',
            [
                'transfer' => [
                    'customer' => $customerId,
                    'points' => 1,
                ],
            ]
        );
        $this->assertOkResponseStatus($this->authenticatedClient->getResponse());

        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'translations' => [
                        'en' => [
                            'name' => 'Bought reward',
                        ],
                    ],
                    'reward' => 'conversion_coupon',
                    'active' => true,
                    'activity' => [
                        'allTime' => true,
                    ],
                    'couponGenerator' => [
                        'characterSet' => 'alphanum',
                        'length' => 5,
                    ],
                    'usageLimit' => [
                        'general' => 100,
                        'perUser' => 100,
                    ],
                    'visibility' => [
                        'allTime' => true,
                    ],
                    'featured' => true,
                    'public' => true,
                    'unitsConversion' => [
                        'ratio' => 1.3,
                        'rounding' => 'default',
                    ],
                ],
            ]
        );
        $response = $this->authenticatedClient->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $rewardId = $data['rewardId'];

        $this->authenticatedClient->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$rewardId.'/buy',
            [
                'customerId' => $customerId,
                'units' => 1,
            ]
        );
        $this->assertOkResponseStatus($this->authenticatedClient->getResponse());

        $filenamePrefix = self::getContainer()->getParameter('oloy.reward.bought.export.filename_prefix');

        $expectedHeaderData = sprintf('attachment; filename="%s', $filenamePrefix);

        $this->authenticatedClient->request('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/redemption/export/csv');

        $response = $this->authenticatedClient->getResponse();

        $this->assertOkResponseStatus($response);
        $this->assertFalse(strpos($expectedHeaderData, $response->headers->get('content-disposition')));
        $this->assertGreaterThan(self::EMPTY_CVS_EXPORT_FILE_LENGTH, $response->headers->get('content-length'));
    }

    /**
     * @test
     */
    public function it_returns_empty_csv_response_when_exports_bought_data_is_empty(): void
    {
        $filenamePrefix = self::getContainer()->getParameter('oloy.reward.bought.export.filename_prefix');

        $expectedHeaderData = sprintf('attachment; filename="%s', $filenamePrefix);

        $this->authenticatedClient->request('GET', '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/redemption/export/csv?redemptionDate[lt]=2016-08-24T14:15:22');

        $response = $this->authenticatedClient->getResponse();

        $this->assertOkResponseStatus($response);
        $this->assertFalse(strpos($expectedHeaderData, $response->headers->get('content-disposition')));
        $this->assertSame((string) self::EMPTY_CVS_EXPORT_FILE_LENGTH, $response->headers->get('content-length'));
    }

    /**
     * @test
     */
    public function it_returns_public_list_of_public_and_active_rewards(): void
    {
        $this->client->request(
            Request::METHOD_GET,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/public',
            [
                'available' => true,
            ]
        );

        $response = $this->client->getResponse();

        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertCount(7, $data['items']);
        $this->assertSame(7, $data['total']['filtered']);
    }

    /**
     * @test
     */
    public function it_returns_public_list_of_featured_and_public_rewards(): void
    {
        $this->client->request(
            Request::METHOD_GET,
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/public',
            [
                'available' => true,
                'featured' => true,
            ]
        );
        $response = $this->client->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);

        foreach ($data['items'] as $reward) {
            $this->assertTrue($reward['featured']);
            $this->assertTrue($reward['public']);
        }
    }

    /**
     * @test
     */
    public function it_returns_an_object_on_empty_segments(): void
    {
        $this->authenticatedClient->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::NOSEGMENTS_REWARD_ID,
        );
        $response = $this->authenticatedClient->getResponse();
        $this->assertNotFalse(strpos($response->getContent(), '"segmentNames":{}'));
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('segmentNames', $data);
    }

    /**
     * @test
     */
    public function it_returns_an_object_on_empty_levels(): void
    {
        $this->authenticatedClient->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.LoadRewardData::NOLEVEL_REWARD_ID,
        );
        $response = $this->authenticatedClient->getResponse();
        $this->assertNotFalse(strpos($response->getContent(), '"levelNames":{}'));
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('levelNames', $data);
    }

    /**
     * @test
     */
    public function it_validate_comment_in_fulfillment_reward(): void
    {
        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward',
            [
                'reward' => [
                    'reward' => 'material',
                    'labels' => [
                    ],
                    'translations' => [
                        'en' => [
                            'name' => 'Tesst',
                        ],
                    ],
                    'visibility' => [
                        'allTime' => true,
                    ],
                    'activity' => [
                        'allTime' => true,
                    ],
                    'active' => true,
                    'usageLimit' => [
                        'general' => 1,
                        'perUser' => 1,
                    ],
                ],
            ],
        );

        $response = $this->authenticatedClient->getResponse();

        $this->assertOkResponseStatus($response);

        $data = json_decode($response->getContent(), true);

        $rewardId = $data['rewardId'];

        $this->authenticatedClient->jsonRequest(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member',
            [
                'customer' => [
                    'email' => '<EMAIL>',
                    'firstName' => 'Kelley',
                    'lastName' => 'Jonnas',
                    'agreement1' => true,
                    'agreement2' => true,
                    'agreement3' => true,
                ],
            ]
        );

        $response = $this->authenticatedClient->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $customerId = $data['customerId'];

        $this->authenticatedClient->request(
            'POST',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/reward/'.$rewardId.'/buy',
            [
                'customerId' => $customerId,
                'quantity' => 1,
                'rewardWalletCode' => null,
                'withoutPoints' => false,
            ]
        );

        $response = $this->authenticatedClient->getResponse();
        $this->assertOkResponseStatus($response);
        $data = json_decode($response->getContent(), true);
        $issuedReward = $data[0]['issuedRewardId'];

        //change status
        $this->authenticatedClient->request(
            'POST',
            sprintf(
                '/api/%s/redemption/%s/status',
                LoadSettingsData::DEFAULT_STORE_CODE,
                $issuedReward
            ),
            [
                'status' => 'pending',
                'comment' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam efficitur,
                 odio vel fringilla malesuada, felis justo consequat purus, id consectetur sapien mi eget ex.
                 Vestibulum euismod, nunc nec sollicitudin vestibulum, lacus lectus volutpat erat,
                 eget efficitur quam tellus sit amet augue. Sed vel',
            ]
        );

        $response = $this->authenticatedClient->getResponse();
        $this->assertBadRequestResponseStatus($response);
        self::assertStringContainsString('This value is too long. It should have 255 characters or less.', $response->getContent());
    }

    public function getRewardsFilters(): array
    {
        return [
            [['featured' => 1, 'public' => 1], 12],
            [['featured' => 0, 'public' => 0], 0],
            [['featured' => 1], 12],
            [['featured' => 0], 7],
            [['public' => 1], 19],
            [['public' => 0], 0],
            [['categories' => ['in' => LoadRewardData::REWARD_CATEGORY2_ID]], 19],
            [['categories' => ['in' => LoadRewardData::REWARD_CATEGORY1_ID]], 5],
            [['categories' => ['in' => LoadRewardData::REWARD_CATEGORY1_ID.','.LoadRewardData::REWARD_CATEGORY2_ID]], 19],
            [['name' => 'zwrot gotówki'], 0],
            [['name' => ['like' => 'test']], 5],
            [['name' => ['like' => 'TEST']], 5],
            [['labels' => ['eq' => '(type;cashback)']], 14],
            [['labels' => ['eq' => '(type;test),(type;promotion)']], 5], // (labels like %{"key":"type","value":"test"}% OR labels like %{"key":"type","value":"promotion"}%)
            [['labels' => ['eq' => '(not-exists;not-exists)']], 0],
        ];
    }

    public function sortParamsProvider(): array
    {
        return [
            ['rewardId', 'asc', 'desc'],
            ['visibleFrom', 'desc', 'asc'],
            ['createdAt', 'asc', 'desc'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->client = self::createClient();
        self::ensureKernelShutdown();
        $this->authenticatedClient = self::createAuthenticatedClient();
        $this->rewardRepository = self::getContainer()->get(RewardRepository::class);
        $this->customerRepository = self::getContainer()->get(CustomerRepositoryInterface::class);
    }

    protected function getDefaultAccount(CustomerId $customerId): array
    {
        $this->authenticatedClient->request(
            'GET',
            '/api/'.LoadSettingsData::DEFAULT_STORE_CODE.'/member/'.$customerId,
        );

        $response = $this->authenticatedClient->getResponse();
        $data = json_decode($response->getContent(), true);

        $this->assertOkResponseStatus($response);
        $this->assertArrayHasKey('defaultAccount', $data);

        return $data['defaultAccount'];
    }

    private function getCustomerDetails($email): Customer
    {
        return $this->customerRepository->findOneBy([
            'email' => $email,
            'storeId' => LoadSettingsData::DEFAULT_STORE_ID,
        ]);
    }
}
