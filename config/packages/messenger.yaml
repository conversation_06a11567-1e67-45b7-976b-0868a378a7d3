framework:
    messenger:
        default_bus: default.bus
        buses:
            default.bus: ~
            sender.bus:
              middleware:
                - bus_logger
            command.bus:
                middleware:
                    - dispatch_data_analytics_batch_after_current_bus_stamp
                    - lock
                    - bus_logger
                    - ignore_exception
                    - convert_known_exceptions
                    - stop_worker_on_exception
                    - doctrine_ping_connection
                    - doctrine_close_connection
                    - command_webhook
                    - doctrine_transaction
                    - command_log
            job.bus:
                middleware:
                    - dispatch_data_analytics_batch_after_current_bus_stamp
                    - lock
                    - bus_logger
                    - ignore_exception
                    - convert_known_exceptions
                    - stop_worker_on_exception
                    - doctrine_ping_connection
                    - doctrine_close_connection
                    - doctrine_transaction
            job.non_transaction.bus:
                middleware:
                    - dispatch_data_analytics_batch_after_current_bus_stamp
                    - lock
                    - bus_logger
                    - ignore_exception
                    - stop_worker_on_exception
                    - doctrine_ping_connection
                    - doctrine_close_connection
            data.analytics.bus:
                middleware:
                    - dispatch_data_analytics_batch_after_current_bus_stamp
                    - lock
                    - bus_logger
                    - ignore_exception
                    - convert_known_exceptions
                    - stop_worker_on_exception
                    - doctrine_ping_connection
                    - doctrine_close_connection
                    - doctrine_transaction
            domain.event.bus:
                default_middleware: allow_no_handlers
            event.bus:
                default_middleware: allow_no_handlers
            public.event.bus:
                default_middleware: allow_no_handlers
                middleware:
                    - doctrine_transaction

        failure_transport: failed

        transports:
            failed: 'doctrine://default?queue_name=failed&table_name=failed_messages'
            failed_transport_webhook: 'doctrine://default?queue_name=failed_transport_webhook&table_name=failed_webhook_messages'
            sync: 'sync://'
            readmodel_proxy:
                dsn: 'readmodel://'
                serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                retry_strategy:
                    max_retries: 0
parameters:
    messenger.lock_retry_count_failed: '%env(int:MESSENGER_LOCK_RETRY_COUNT_FAILED)%'
    messenger.short_lock_ttl: '%env(int:MESSENGER_SHORT_LOCK_TTL)%'
    messenger.long_lock_ttl: '%env(int:MESSENGER_LONG_LOCK_TTL)%'
    messenger.segment_lock_ttl: '%env(int:MESSENGER_SEGMENT_LOCK_TTL)%'

when@prod: &messenger_prod
    framework:
        messenger:
            transports: &messenger_prod_transports
                high_async: &messenger_prod_high_async
                    dsn: '%env(MESSENGER_TRANSPORT_HIGH_DSN)%'
                    options:
                        auto_setup: false
                        buffer_size: '%env(MESSENGER_TRANSPORT_BUFFER_SIZE)%'
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                high_p1_async: &messenger_prod_high_p1_async
                    dsn: '%env(MESSENGER_TRANSPORT_HIGH_P1_DSN)%'
                    options:
                        auto_setup: false
                        buffer_size: '%env(MESSENGER_TRANSPORT_BUFFER_SIZE)%'
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                high_p2_async: &messenger_prod_high_p2_async
                    dsn: '%env(MESSENGER_TRANSPORT_HIGH_P2_DSN)%'
                    options:
                        auto_setup: false
                        buffer_size: '%env(MESSENGER_TRANSPORT_BUFFER_SIZE)%'
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                high_p3_async: &messenger_prod_high_p3_async
                    dsn: '%env(MESSENGER_TRANSPORT_HIGH_P3_DSN)%'
                    options:
                        auto_setup: false
                        buffer_size: '%env(MESSENGER_TRANSPORT_BUFFER_SIZE)%'
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                medium_async: &messenger_prod_medium_async
                    dsn: '%env(MESSENGER_TRANSPORT_MEDIUM_DSN)%'
                    options:
                        auto_setup: false
                        buffer_size: '%env(MESSENGER_TRANSPORT_BUFFER_SIZE)%'
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                low_async: &messenger_prod_low_async
                    dsn: '%env(MESSENGER_TRANSPORT_LOW_DSN)%'
                    options:
                        auto_setup: false
                        buffer_size: '%env(MESSENGER_TRANSPORT_BUFFER_SIZE)%'
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                readmodel_async: &messenger_prod_readmodel_async
                    dsn: '%env(MESSENGER_TRANSPORT_READMODEL_DSN)%'
                    options:
                        auto_setup: false
                        buffer_size: '%env(MESSENGER_TRANSPORT_BUFFER_SIZE)%'
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                long_process_async: &messenger_prod_long_process_async
                    dsn: '%env(MESSENGER_TRANSPORT_LONG_PROCESS_DSN)%'
                    options:
                        auto_setup: false
                        buffer_size: 1
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: 0
                dead_letter_async: &messenger_prod_dead_letter_async
                    dsn: '%env(MESSENGER_TRANSPORT_DEAD_LETTER_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: 0
                webhook_request_async: &messenger_prod_webhook_request_async
                    dsn: '%env(MESSENGER_TRANSPORT_WEBHOOK_REQUEST_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        buffer_size: '%env(WEBHOOKS_TRANSPORT_BUFFER_SIZE)%'
                        debug: '%env(bool:WEBHOOKS_DEBUG)%'
                        poll_timeout: '%env(float:WEBHOOKS_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:WEBHOOKS_MAX_RETRIES)%'
                        delay: 1000
                    failure_transport: failed_transport_webhook
                campaign_time_prepare_async: &messenger_prod_campaign_time_prepare_async
                    dsn: '%env(MESSENGER_TRANSPORT_CAMPAIGN_TIME_PREPARE_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        buffer_size: 1
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                    retry_strategy:
                        max_retries: 0
                campaign_time_process_async: &messenger_prod_campaign_time_process_async
                    dsn: '%env(MESSENGER_TRANSPORT_CAMPAIGN_TIME_PROCESS_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        buffer_size: 1
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                    retry_strategy:
                        max_retries: 0
                campaign_time_run_async: &messenger_prod_campaign_time_run_async
                    dsn: '%env(MESSENGER_TRANSPORT_CAMPAIGN_TIME_RUN_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        buffer_size: '%env(MESSENGER_TRANSPORT_BUFFER_SIZE)%'
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                segment_recreate_async: &messenger_prod_segment_recreate_async
                    dsn: '%env(MESSENGER_TRANSPORT_SEGMENT_RECREATE_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        buffer_size: 1
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                    retry_strategy:
                        max_retries: 0
                data_analytics_async: &messenger_prod_data_analytics_async
                    dsn: '%env(MESSENGER_TRANSPORT_DATA_ANALYTICS_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        buffer_size: '%env(MESSENGER_TRANSPORT_BUFFER_SIZE)%'
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                data_analytics_batch_dispatch_async: &messenger_prod_data_analytics_batch_dispatch_async
                    dsn: '%env(MESSENGER_TRANSPORT_DATA_ANALYTICS_BATCH_DISPATCH_DSN)%'
                    options:
                        auto_setup: false
                        buffer_size: 1
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                import_start_process_items_async: &messenger_prod_import_start_process_items_async
                    dsn: '%env(MESSENGER_TRANSPORT_IMPORT_START_PROCESS_ITEMS_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        buffer_size: 1
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                import_process_file_async: &messenger_prod_import_process_file_async
                    dsn: '%env(MESSENGER_TRANSPORT_IMPORT_PROCESS_FILE_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        buffer_size: '%env(MESSENGER_TRANSPORT_IMPORT_PROCESS_FILE_BUFFER_SIZE)%'
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                import_process_item_async: &messenger_prod_import_process_item_async
                    dsn: '%env(MESSENGER_TRANSPORT_IMPORT_PROCESS_ITEM_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        buffer_size: '%env(MESSENGER_TRANSPORT_BUFFER_SIZE)%'
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                bulk_action_start_process_items_async: &messenger_prod_bulk_action_start_process_items_async
                    dsn: '%env(MESSENGER_TRANSPORT_BULK_ACTION_START_PROCESS_ITEMS_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        buffer_size: 1
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                bulk_action_process_items_async: &messenger_prod_bulk_action_process_items_async
                    dsn: '%env(MESSENGER_TRANSPORT_BULK_ACTION_PROCESS_ITEMS_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        buffer_size: '%env(MESSENGER_TRANSPORT_BUFFER_SIZE)%'
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                tier_batch_evaluate_member_async: &messenger_prod_tier_batch_evaluate_member_async
                    dsn: '%env(MESSENGER_TRANSPORT_BATCH_EVALUATE_MEMBER_TIER_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        buffer_size: 1
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                tier_evaluate_member_async: &messenger_prod_tier_evaluate_member_async
                    dsn: '%env(MESSENGER_TRANSPORT_EVALUATE_MEMBER_TIER_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000
                leaderboard_event_async: &messenger_prod_leaderboard_event_async
                    dsn: '%env(MESSENGER_TRANSPORT_LEADERBOARD_EVENT_DSN)%'
                    serializer: OpenLoyalty\Core\Infrastructure\Message\Serializer\JmsMessengerSerializer
                    options:
                        auto_setup: false
                        debug: '%env(bool:MESSENGER_DEBUG)%'
                        poll_timeout: '%env(float:MESSENGER_POOL_TIMEOUT)%'
                    retry_strategy:
                        max_retries: '%env(int:MESSENGER_MAX_RETRIES)%'
                        delay: 1000

                failed:
                    dsn: 'doctrine://default?queue_name=failed&table_name=failed_messages'
                    retry_strategy:
                        service: OpenLoyalty\Core\Infrastructure\Message\AlwaysRetryMessengerStrategy
                failed_transport_webhook:
                    dsn: 'doctrine://default?queue_name=failed_transport_webhook&table_name=failed_webhook_messages'
                    retry_strategy:
                        service: OpenLoyalty\Core\Infrastructure\Message\AlwaysRetryMessengerStrategy
                sync: 'sync://'
            routing: &messenger_prod_routing
                'OpenLoyalty\CustomEvent\Application\Command\CreateCustomEventCommand': high_async
                'OpenLoyalty\Transaction\Application\Command\RegisterTransaction': high_async

                'OpenLoyalty\User\Application\Job\MatchCustomEvent': high_p1_async
                'OpenLoyalty\User\Application\Job\MatchTransaction': high_p1_async

                'OpenLoyalty\Achievement\Application\Job\RunAchievement': high_p2_async

                'OpenLoyalty\Campaign\Application\Job\RunCampaign': high_p3_async

                'OpenLoyalty\User\Application\Command\RegisterCustomer': medium_async
                'OpenLoyalty\Messaging\Domain\Model\MessageInterface': medium_async
                'OpenLoyalty\Messaging\Application\Webhook\Job\DispatchWebhookEvent': medium_async
                'OpenLoyalty\Messaging\Application\Webhook\Job\DispatchWebhookRequest': webhook_request_async
                'OpenLoyalty\Messaging\Domain\Webhook\DispatchWebhookPayload': webhook_request_async
                'OpenLoyalty\Segment\Application\Job\RecreateSegmentJob': segment_recreate_async

                'OpenLoyalty\Audit\Application\Job\BaseAuditLogJob': low_async
                'OpenLoyalty\Campaign\Application\Job\RunTimeCampaign': low_async
                'OpenLoyalty\Campaign\Application\Job\ProcessTimeCampaignForMember': low_async
                'OpenLoyalty\Campaign\Application\Job\CreateCampaignCodesJob': low_async
                'OpenLoyalty\Campaign\Application\Job\ProcessCampaignCodesJob': low_async
                'OpenLoyalty\Level\Application\Command\SendExpireLevelNotification': low_async
                'OpenLoyalty\Import\Application\Job\ProcessImportItemsJob': low_async
                'OpenLoyalty\Points\Application\Job\ExpireTransferBatch': low_async
                'OpenLoyalty\Points\Application\Job\UnlockTransferBatch': low_async
                'OpenLoyalty\User\Application\Job\CustomerHasBecomeActiveBatch': low_async
                'OpenLoyalty\User\Application\Job\CustomerHasBecomeInactiveBatch': low_async
                'OpenLoyalty\Points\Application\Job\PointsWillExpireWebhookNotificationsJob': low_async

                'OpenLoyalty\Core\Domain\ReadModel\Request\RequestProjectionInterface': readmodel_async

                'OpenLoyalty\Import\Application\Job\ProcessFileContentJob': long_process_async
                'OpenLoyalty\Export\Application\Job\ProcessExport': long_process_async
                'OpenLoyalty\Account\Application\Job\CreateWalletsForTypeJob': long_process_async
                'OpenLoyalty\Achievement\Application\Job\RefreshMembersAchievementProgress': long_process_async
                'OpenLoyalty\Core\Application\Job\RebuildAsyncCache': long_process_async

                'OpenLoyalty\Campaign\Application\Job\PrepareTimeCampaign': campaign_time_prepare_async
                'OpenLoyalty\Campaign\Application\Job\ProcessTimeCampaign': campaign_time_process_async
                'OpenLoyalty\Campaign\Application\Job\RunTimeCampaignRequest': campaign_time_run_async

                'OpenLoyalty\Import\Application\Job\StartProcessItemsJob': import_start_process_items_async

                'OpenLoyalty\Tools\BulkActions\Application\Job\StartProcessBulkActionItemsJob': bulk_action_start_process_items_async
                'OpenLoyalty\Tools\BulkActions\Application\Job\ProcessBulkActionItemsJob': bulk_action_process_items_async
                'OpenLoyalty\Tools\BulkActions\Application\Job\ProcessBulkActionJob': long_process_async

                'OpenLoyalty\DataAnalytics\Domain\Shared\Message\AnalyticsEventInterface': data_analytics_async
                'OpenLoyalty\DataAnalytics\Application\Job\DispatchBatchMessagesJob': data_analytics_batch_dispatch_async
                'OpenLoyalty\Level\Application\Job\BatchEvaluateMemberTierJob': tier_batch_evaluate_member_async
                'OpenLoyalty\Level\Application\Job\EvaluateMemberTierJob': tier_evaluate_member_async
                'OpenLoyalty\Leaderboard\Application\Job\ProcessLeaderboardTransferJob': leaderboard_event_async

when@diag: *messenger_prod

when@diag_cli: *messenger_prod

when@prod_rabbit: &messenger_prod_rabbit
    <<: *messenger_prod
    framework:
        messenger:
            transports:
                <<: *messenger_prod_transports
                high_async:
                    <<: *messenger_prod_high_async
                    options:
                        auto_setup: false
                high_p1_async:
                    <<: *messenger_prod_high_p1_async
                    options:
                        auto_setup: false
                high_p2_async:
                    <<: *messenger_prod_high_p2_async
                    options:
                        auto_setup: false
                high_p3_async:
                    <<: *messenger_prod_high_p3_async
                    options:
                        auto_setup: false
                medium_async:
                    <<: *messenger_prod_medium_async
                    options:
                        auto_setup: false
                low_async:
                    <<: *messenger_prod_low_async
                    options:
                        auto_setup: false
                readmodel_async:
                    <<: *messenger_prod_readmodel_async
                    options:
                        auto_setup: false
                long_process_async:
                    <<: *messenger_prod_long_process_async
                    options:
                        auto_setup: false
                dead_letter_async:
                    <<: *messenger_prod_dead_letter_async
                    options:
                        auto_setup: false
                webhook_request_async:
                    <<: *messenger_prod_webhook_request_async
                    options:
                        auto_setup: false
                campaign_time_prepare_async:
                    <<: *messenger_prod_campaign_time_prepare_async
                    options:
                        auto_setup: false
                campaign_time_process_async:
                    <<: *messenger_prod_campaign_time_process_async
                    options:
                        auto_setup: false
                campaign_time_run_async:
                    <<: *messenger_prod_campaign_time_run_async
                    options:
                        auto_setup: false
                segment_recreate_async:
                    <<: *messenger_prod_segment_recreate_async
                    options:
                        auto_setup: false
                data_analytics_async:
                    <<: *messenger_prod_data_analytics_async
                    options:
                        auto_setup: false
                data_analytics_batch_dispatch_async:
                    <<: *messenger_prod_data_analytics_batch_dispatch_async
                    options:
                        auto_setup: false
                import_start_process_items_async:
                    <<: *messenger_prod_import_start_process_items_async
                    options:
                        auto_setup: false
                import_process_file_async:
                    <<: *messenger_prod_import_process_file_async
                    options:
                        auto_setup: false
                import_process_item_async:
                    <<: *messenger_prod_import_process_item_async
                    options:
                        auto_setup: false
                bulk_action_start_process_items_async:
                    <<: *messenger_prod_bulk_action_start_process_items_async
                    options:
                        auto_setup: false
                bulk_action_process_items_async:
                    <<: *messenger_prod_bulk_action_process_items_async
                    options:
                        auto_setup: false
                tier_batch_evaluate_member_async:
                    <<: *messenger_prod_tier_batch_evaluate_member_async
                    options:
                        auto_setup: false
                tier_evaluate_member_async:
                    <<: *messenger_prod_tier_evaluate_member_async
                    options:
                        auto_setup: false
                leaderboard_event_async:
                    <<: *messenger_prod_leaderboard_event_async
                    options:
                        auto_setup: false
            routing:
                <<: *messenger_prod_routing

when@test_async: *messenger_prod_rabbit

when@dev:
    framework:
        messenger:
            routing: ~

when@test:
    framework:
        messenger:
            routing: ~
